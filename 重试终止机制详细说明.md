# 违规内容重试终止机制详细说明

## 🎯 概述

本文档详细说明违规内容检测时如何终止**所有类型的重试机制**，确保在检测到内容违规时立即停止所有无效的重试尝试，包括数据中心重试、代理重试、任务级重试等。

## 🏗️ 重试层级架构

```
任务级重试 (最外层)
├── 数据中心重试 (中间层)
│   ├── 代理集群重试 (代理层)
│   │   ├── 多代理故障转移 (单次集群尝试)
│   │   └── 单代理重试循环 (单个代理的重试)
│   └── 直连API重试
└── 最终错误处理
```

## 🛡️ 四层重试终止机制

### 1. 任务级重试终止 (最外层防护)

**位置**：`runSingleTtsProcess` 函数的重试循环
**作用**：防止违规任务在不同策略间重试

```javascript
// 任务级重试循环
for (let attempt = 1; attempt <= maxAttempts; attempt++) {
  try {
    // 执行TTS任务（包含所有下层重试）
    const result = await executeTtsTask(strategy);
    return result;
  } catch (error) {
    // 【关键】检测违规并立即终止所有任务重试
    if (error.isContentViolation) {
      if (retryConfig.ENABLE_TASK_RETRY_DEBUG) {
        this.logger.warn('Content violation detected. Immediately terminating task retries.', {
          attempt: attempt,
          error: error.message,
          isContentViolation: true
        });
      }
      break; // 立即跳出任务重试循环
    }
    
    // 其他错误继续尝试下一个策略
    if (attempt < maxAttempts) {
      await applyNextRetryStrategy(attempt + 1);
    }
  }
}
```

**终止效果**：
- ✅ 阻止切换到其他任务重试策略
- ✅ 阻止数据中心切换
- ✅ 阻止代理模式切换
- ✅ 立即进入最终错误处理

### 2. 数据中心级重试终止 (中间层防护)

**位置**：`isDataCenterRetryableError` 函数
**作用**：防止违规错误被误判为可重试的数据中心错误

```javascript
function isDataCenterRetryableError(error, status, originalErrorData = null) {
  // 【最高优先级】检查内容违规
  if (isContentViolationError(status, originalErrorData, error.message)) {
    return false; // 违规错误绝对不可重试
  }
  
  // 检查其他可重试条件
  if (status === 429) return true; // 配额限制
  if (status === 503) return true; // 服务不可用
  
  // 检查错误消息中的可重试关键词
  const retryableKeywords = ['quota', 'rate limit', 'capacity'];
  return retryableKeywords.some(keyword => 
    error.message?.toLowerCase().includes(keyword)
  );
}
```

**终止效果**：
- ✅ 防止违规错误触发数据中心切换
- ✅ 防止违规错误触发配额重试
- ✅ 确保违规错误被正确分类

### 3. 代理重试机制终止 (代理层防护)

#### 3.1 代理集群重试终止

**位置**：`callTtsProxyWithFailover` 函数的集群重试循环
**作用**：防止违规错误触发整个代理集群的重试

```javascript
// 代理集群重试循环
for (let clusterAttempt = 1; clusterAttempt <= clusterRetryAttempts; clusterAttempt++) {
  try {
    // 尝试整个代理集群（包含多代理故障转移）
    return await tryEntireProxyCluster(proxyUrls, payload, proxyConfig);
  } catch (error) {
    // 【关键】如果是违规错误，立即终止集群重试
    if (error.isContentViolation) {
      if (proxyConfig.ENABLE_PROXY_DEBUG) {
        console.warn(`[PROXY-CLUSTER] Content violation detected. Terminating cluster retries.`);
      }
      throw error; // 立即向上传播，不进行集群重试
    }
    
    // 其他错误在最后一次集群尝试时抛出
    if (clusterAttempt === clusterRetryAttempts) {
      throw error;
    }
    
    // 等待后进行下一次集群重试
    const backoffDelay = Math.pow(2, clusterAttempt - 1) * 1000;
    await new Promise(resolve => setTimeout(resolve, backoffDelay));
  }
}
```

#### 3.2 多代理故障转移终止

**位置**：`callTtsProxyWithFailover` 函数的代理循环
**作用**：防止违规错误在多个代理间切换

```javascript
// 多代理故障转移循环
for (let i = 0; i < proxyUrls.length; i++) {
  const proxyUrl = proxyUrls[i];
  
  try {
    const response = await fetch(proxyUrl, requestConfig);
    
    if (!response.ok) {
      const errorData = await response.json();
      const originalMessage = errorData?.detail?.message || errorData?.message;
      
      // 【关键】检测违规并立即终止所有代理尝试
      if (isContentViolationError(response.status, errorData, originalMessage)) {
        const violationError = new Error(originalMessage);
        violationError.status = response.status;
        violationError.isContentViolation = true;
        violationError.isDataCenterRetryable = false;
        
        if (proxyConfig.ENABLE_PROXY_DEBUG) {
          console.warn(`[PROXY-FAILOVER] Content violation from Proxy #${i + 1}. Terminating failover.`);
        }
        
        throw violationError; // 立即抛出，不尝试其他代理
      }
      
      // 其他错误继续尝试下一个代理
      if (i === proxyUrls.length - 1) {
        throw new Error(`All ${proxyUrls.length} proxies failed`);
      }
    } else {
      // 成功响应，返回结果
      return await response.arrayBuffer();
    }
  } catch (error) {
    // 网络错误或违规错误处理
    if (error.isContentViolation) {
      throw error; // 违规错误立即向上传播
    }
    
    // 网络错误在最后一个代理时抛出
    if (i === proxyUrls.length - 1) {
      throw error;
    }
  }
}
```

#### 3.3 单代理重试循环终止

**位置**：`callVercelProxyFallback` 函数的重试循环
**作用**：防止违规错误在单个代理上重复尝试

```javascript
// 单代理重试循环
for (let attempt = 1; attempt <= maxRetries; attempt++) {
  try {
    const response = await fetch(proxyUrl, requestConfig);
    
    if (!response.ok) {
      const errorData = await response.json();
      const originalMessage = errorData?.detail?.message || errorData?.message;
      
      // 【关键】检测违规并立即终止重试循环
      if (isContentViolationError(response.status, errorData, originalMessage)) {
        const violationError = new Error(originalMessage);
        violationError.status = response.status;
        violationError.isContentViolation = true;
        violationError.isDataCenterRetryable = false;
        
        if (proxyConfig.ENABLE_PROXY_DEBUG) {
          console.error(`[PROXY-FALLBACK] Content violation detected. Terminating retries.`);
        }
        
        throw violationError; // 立即抛出，不进行重试
      }
      
      // 其他错误在最后一次尝试时抛出
      if (attempt === maxRetries) {
        throw new Error(`Proxy failed after ${maxRetries} attempts`);
      }
    } else {
      // 成功响应，返回结果
      return await response.arrayBuffer();
    }
  } catch (error) {
    // 错误处理
    if (error.isContentViolation) {
      throw error; // 违规错误立即终止重试
    }
    
    // 其他错误在最后一次尝试时抛出
    if (attempt === maxRetries) {
      throw error;
    }
  }
  
  // 等待后重试（只有非违规错误才会到达这里）
  const retryDelay = Math.pow(2, attempt - 1) * 1000;
  await new Promise(resolve => setTimeout(resolve, retryDelay));
}
```

### 4. 直连API重试终止

**位置**：`generateSpeech` 函数的直连重试循环
**作用**：防止违规错误在直连API上重复尝试

```javascript
// 直连API重试循环
for (let retries = maxRetries; retries >= 0; retries--) {
  try {
    const response = await fetch(url, requestConfig);
    
    if (!response.ok) {
      const errorData = await response.json();
      const errorMessage = errorData?.detail?.message || errorData?.message;
      
      // 【关键】检测违规并立即终止直连重试
      if (isContentViolationError(response.status, errorData, errorMessage)) {
        const violationError = new Error(errorMessage);
        violationError.status = response.status;
        violationError.isContentViolation = true;
        violationError.isDataCenterRetryable = false;
        
        throw violationError; // 立即抛出，不进行重试
      }
      
      // 其他错误处理
      const enhancedError = new Error(errorMessage);
      enhancedError.status = response.status;
      enhancedError.isDataCenterRetryable = isDataCenterRetryableError(enhancedError, response.status, errorData);
      
      if (retries === 0) {
        throw enhancedError; // 最后一次尝试失败
      }
    } else {
      // 成功响应
      return await response.arrayBuffer();
    }
  } catch (error) {
    if (error.isContentViolation) {
      throw error; // 违规错误立即终止
    }
    
    if (retries === 0) {
      throw error; // 最后一次尝试失败
    }
  }
  
  // 等待后重试
  await new Promise(resolve => setTimeout(resolve, 1000));
}
```

## 🔄 完整终止流程

### 违规检测时的终止顺序

```
1. API响应 → 检测到403 + 违规消息
2. 设置error.isContentViolation = true
3. 立即抛出错误，跳过当前层级的所有重试
4. 错误向上传播到下一个重试层级
5. 上层检测到isContentViolation，立即终止该层重试
6. 继续向上传播，直到任务级处理
7. 任务级检测到违规，终止所有任务重试
8. 进入最终错误处理，返回违规消息给用户
```

### 关键检查点

```javascript
// 在每个重试层级都要检查
if (error.isContentViolation) {
  // 记录日志（可选）
  console.warn('Content violation detected. Terminating retries at this level.');
  
  // 立即终止当前层级的重试
  throw error; // 或 break; 或 return;
}
```

## 📊 终止效果对比

| 重试类型 | 实施前 | 实施后 |
|----------|--------|--------|
| 任务级重试 | 继续尝试其他策略 | ✅ 立即终止 |
| 数据中心重试 | 可能被误判为可重试 | ✅ 立即识别并终止 |
| 代理集群重试 | 继续尝试整个集群 | ✅ 立即终止集群重试 |
| 多代理故障转移 | 继续尝试其他代理 | ✅ 立即终止故障转移 |
| 单代理重试 | 继续重试同一代理 | ✅ 立即终止重试循环 |
| 直连API重试 | 继续重试直连 | ✅ 立即终止直连重试 |

## 🎯 实施要点

### 1. 检测准确性
- 使用`isContentViolationError`函数精确识别违规
- 避免误判其他403错误
- 支持不同API响应格式

### 2. 标志一致性
- 所有层级都检查`error.isContentViolation`
- 保持错误对象的完整性
- 确保标志正确传播

### 3. 终止彻底性
- 每个重试层级都要有检查
- 使用适当的终止方式（throw/break/return）
- 确保不会遗漏任何重试路径

### 4. 向后兼容
- 不影响其他错误的重试逻辑
- 保持原有的错误处理机制
- 确保非违规错误正常重试

## 🧪 验证方法

### 测试用例
1. **违规内容测试**：提交违规内容，验证所有重试都被终止
2. **网络错误测试**：模拟网络错误，验证重试机制正常工作
3. **混合错误测试**：同时有违规和网络错误，验证违规优先处理
4. **边界条件测试**：在不同重试层级触发违规，验证终止效果

### 关键指标
- 违规响应时间：应该<1秒
- 无效重试次数：应该为0
- 错误传播完整性：违规错误应该正确到达用户
- 其他错误不受影响：非违规错误的重试机制正常

这个四层重试终止机制确保了违规内容在任何层级被检测到时，都能立即终止所有相关的重试尝试，实现真正的"快速失败"效果。
