// 完善后的快速失败机制测试
// 验证retryFailedChunks函数的快速失败功能

console.log('🚀 开始完善后的快速失败机制测试');

// 模拟环境
const mockEnv = {
  DEBUG: true,
  _log: {
    debug: (msg, data) => console.log(`[DEBUG] ${msg}`, data),
    warn: (msg, data) => console.log(`[WARN] ${msg}`, data),
    info: (msg, data) => console.log(`[INFO] ${msg}`, data)
  }
};

// 模拟违规检测函数
function isContentViolationError(status, errorData, errorMessage) {
  if (status !== 403) return false;
  if (errorData?.detail?.status === 'content_against_policy') return true;
  const violationMessage = "We are sorry but text you are trying to use may violate our Terms of Service and has been blocked.";
  if (errorMessage && errorMessage.includes(violationMessage)) return true;
  const violationKeywords = ["violate our Terms", "content_against_policy"];
  const lowerErrorMessage = errorMessage?.toLowerCase() || '';
  return violationKeywords.some(keyword => lowerErrorMessage.includes(keyword.toLowerCase()));
}

// 模拟generateSpeech函数
async function mockGenerateSpeech(text, voiceId, modelId, stability, similarity_boost, style, speed, env, context = {}, signal = null) {
  return new Promise((resolve, reject) => {
    // 监听中止信号
    if (signal) {
      signal.addEventListener('abort', () => {
        console.log(`   generateSpeech: 收到中止信号，停止处理: "${text.substring(0, 20)}..."`);
        reject(new Error('Request aborted'));
      });
    }
    
    // 模拟处理延迟
    const delay = Math.random() * 800 + 200;
    
    setTimeout(() => {
      if (signal?.aborted) {
        reject(new Error('Request aborted'));
        return;
      }
      
      // 模拟违规检测
      if (text.includes('违规')) {
        const error = new Error('We are sorry but text you are trying to use may violate our Terms of Service and has been blocked.');
        error.status = 403;
        error.isContentViolation = true;
        reject(error);
      } else {
        resolve(new ArrayBuffer(1024)); // 模拟音频数据
      }
    }, delay);
  });
}

// 模拟并发限制器
function createMockLimiter() {
  return async (fn) => fn();
}

// 完善后的retryFailedChunks函数（从worker.js复制）
async function retryFailedChunks(failedResults, originalChunks, voiceId, modelId, stability, similarity_boost, style, speed, limiter, env, context = {}) {
  // 【新增】创建AbortController用于重试阶段的快速失败
  const retryAbortController = new AbortController();
  let firstViolationError = null;

  if (env._log) {
    env._log.debug(`Starting retry for ${failedResults.length} failed chunks with fast-fail support`, {
      failedChunkCount: failedResults.length,
      taskId: context.taskId || 'N/A'
    });
  } else if (env.DEBUG) {
    console.log(`[RETRY-FAST-FAIL] Starting retry for ${failedResults.length} failed chunks with fast-fail support`);
  }

  const retryTasks = failedResults.map(failedResult =>
    limiter(async () => {
      // 【新增】检查是否已被中止
      if (retryAbortController.signal.aborted) {
        throw new Error(`Retry chunk ${failedResult.index + 1} cancelled due to violation in another retry`);
      }

      try {
        if (env.DEBUG) {
          console.log(`Retrying chunk ${failedResult.index + 1}...`);
        }
        const chunk = originalChunks[failedResult.index];
        
        // 【关键修改】传递signal参数给generateSpeech，启用重试阶段的快速失败
        const audioData = await mockGenerateSpeech(chunk, voiceId, modelId, stability, similarity_boost, style, speed, env, context, retryAbortController.signal);
        
        if (!audioData) {
          throw new Error(`Retry failed to generate audio for chunk ${failedResult.index + 1}`);
        }
        return { index: failedResult.index, audioData, success: true };
      } catch (error) {
        // 【新增】检测违规并立即中止所有重试
        if (error.isContentViolation && !firstViolationError) {
          firstViolationError = error;
          retryAbortController.abort(); // 立即中止所有其他重试任务
          
          if (env._log) {
            env._log.warn('[RETRY-FAST-FAIL] Violation detected during retry, aborting all retry tasks...', {
              chunkIndex: failedResult.index + 1,
              error: error.message
            });
          } else if (env.DEBUG) {
            console.warn(`[RETRY-FAST-FAIL] Violation detected in retry chunk ${failedResult.index + 1}, aborting all retry tasks...`);
          }
        }

        console.error(`Retry failed for chunk ${failedResult.index + 1}:`, error);
        // 【修复】保留错误对象的完整信息，包括isDataCenterRetryable标志
        return { index: failedResult.index, error: error, success: false };
      }
    })
  );

  const retryResults = await Promise.allSettled(retryTasks);
  
  // 【新增】优先检查违规错误并立即传播
  if (firstViolationError) {
    if (env._log) {
      env._log.warn('[RETRY-FAST-FAIL] Propagating violation error from retry phase', {
        error: firstViolationError.message,
        isContentViolation: true
      });
    } else if (env.DEBUG) {
      console.warn('[RETRY-FAST-FAIL] Propagating violation error from retry phase:', firstViolationError.message);
    }
    throw firstViolationError; // 立即传播，跳过所有其他处理
  }

  return retryResults.map(result => result.status === 'fulfilled' ? result.value : { success: false, error: result.reason.message });
}

// 测试1: 重试阶段的快速失败
async function testRetryFastFail() {
  console.log('\n🧪 测试1: 重试阶段的快速失败机制');
  
  const originalChunks = [
    '正常文本块1',
    '正常文本块2', 
    '包含违规内容的文本块',
    '正常文本块3',
    '正常文本块4'
  ];
  
  // 模拟失败的结果（需要重试）
  const failedResults = [
    { index: 0, error: new Error('Network error'), success: false },
    { index: 2, error: new Error('Timeout'), success: false },
    { index: 3, error: new Error('Server error'), success: false },
    { index: 4, error: new Error('Rate limit'), success: false }
  ];
  
  const limiter = createMockLimiter();
  const context = { taskId: 'test-task-123' };
  
  try {
    await retryFailedChunks(failedResults, originalChunks, 'voice1', 'model1', 0.5, 0.5, 0, 1.0, limiter, mockEnv, context);
    console.log('❌ 应该抛出违规错误但没有抛出');
    return false;
  } catch (error) {
    if (error.isContentViolation) {
      console.log(`✅ 正确捕获并传播违规错误: ${error.message}`);
      return true;
    } else {
      console.log(`❌ 捕获了错误但不是违规错误: ${error.message}`);
      return false;
    }
  }
}

// 测试2: 重试阶段的正常处理
async function testRetryNormalProcessing() {
  console.log('\n🧪 测试2: 重试阶段的正常处理');
  
  const originalChunks = [
    '正常文本块1',
    '正常文本块2',
    '正常文本块3'
  ];
  
  const failedResults = [
    { index: 0, error: new Error('Network error'), success: false },
    { index: 2, error: new Error('Timeout'), success: false }
  ];
  
  const limiter = createMockLimiter();
  const context = { taskId: 'test-task-456' };
  
  try {
    const results = await retryFailedChunks(failedResults, originalChunks, 'voice1', 'model1', 0.5, 0.5, 0, 1.0, limiter, mockEnv, context);
    
    const successful = results.filter(r => r.success).length;
    console.log(`✅ 重试成功处理了 ${successful}/${results.length} 个块`);
    
    return successful > 0;
  } catch (error) {
    console.log(`❌ 正常重试失败: ${error.message}`);
    return false;
  }
}

// 测试3: 模拟完整的processChunks流程
async function testCompleteProcessFlow() {
  console.log('\n🧪 测试3: 完整的processChunks流程（包含重试阶段违规检测）');
  
  // 模拟processChunks中的重试逻辑
  async function mockProcessChunksRetryLogic() {
    const chunks = ['正常文本1', '正常文本2', '违规内容', '正常文本3'];
    const failedResults = [
      { index: 1, error: new Error('Network error'), success: false },
      { index: 2, error: new Error('Timeout'), success: false }
    ];
    
    console.log(`模拟重试 ${failedResults.length} 个失败的块...`);
    
    let finalFailedResults = [];
    
    try {
      const retryResults = await retryFailedChunks(
        failedResults, 
        chunks, 
        'voice1', 
        'model1', 
        0.5, 0.5, 0, 1.0, 
        createMockLimiter(), 
        mockEnv, 
        { taskId: 'test-complete-789' }
      );
      
      // 这里不应该执行到，因为应该抛出违规错误
      console.log('❌ 重试完成但没有检测到违规');
      return false;
      
    } catch (error) {
      if (error.isContentViolation) {
        console.log('✅ 在重试阶段正确检测到违规并立即终止');
        console.log(`   违规错误: ${error.message}`);
        return true;
      } else {
        console.log(`❌ 捕获了错误但不是违规错误: ${error.message}`);
        return false;
      }
    }
  }
  
  return await mockProcessChunksRetryLogic();
}

// 运行所有测试
async function runAllTests() {
  const results = [];
  
  results.push(await testRetryFastFail());
  results.push(await testRetryNormalProcessing());
  results.push(await testCompleteProcessFlow());
  
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  console.log('\n' + '='.repeat(60));
  console.log(`🎯 完善后快速失败机制测试总结: ${passed}/${total} 通过`);
  
  if (passed === total) {
    console.log('✅ 所有测试通过！retryFailedChunks快速失败机制完善成功。');
    console.log('\n🎉 验证结果:');
    console.log('   - retryFailedChunks支持AbortController');
    console.log('   - 重试阶段能够检测违规并立即中止');
    console.log('   - 违规错误能够正确传播到上层');
    console.log('   - processChunks能够捕获重试阶段的违规错误');
    console.log('   - 快速失败机制在所有阶段都能正常工作');
  } else {
    console.log('❌ 部分测试失败，需要进一步检查。');
  }
  
  return passed === total;
}

// 执行测试
runAllTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('测试执行出错:', error);
  process.exit(1);
});
