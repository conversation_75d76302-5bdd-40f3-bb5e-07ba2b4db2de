# 后端改造验证指南

## 🎯 改造概述

本次改造将用户认证、注册、密码管理、配额查询、卡密激活等功能迁移到主后端API，同时保留TTS任务处理的现有逻辑。

## 🔧 环境变量配置

### 启用主后端API模式
```bash
# 设置主后端API基础URL
MAIN_BACKEND_BASE=https://your-main-backend.com

# 其他必要的环境变量
JWT_SECRET=your-jwt-secret

# B后端API配置（用于配额管理）
B_BACKEND_API_TOKEN=B_Backend_API_2024_SecureToken_X9k2#mP8$vL3nQ5@jR7wY4*tZ6

# 配额验证降级控制（已废弃，保留用于向后兼容）
# 新的配额管理策略：
# - 配额验证（checkVip）：始终采用严格模式，API失败时任务失败
# - 配额扣除（updateUserUsage）：始终允许降级到KV存储，确保用户体验
# QUOTA_FALLBACK_ENABLED=false  # 此变量已不再使用
```

### 降级到KV存储模式
```bash
# 不设置MAIN_BACKEND_BASE或设置为空值
# MAIN_BACKEND_BASE=

# 系统将自动使用KV存储作为降级方案
```

## 🔧 配额管理策略（优化版）

### 策略说明

#### 新的配额管理策略
基于实际业务需求，采用差异化的降级策略：

**1. 配额验证（checkVip）- 严格模式**
- **B后端API未配置**：任务直接失败，返回配置错误
- **B后端API调用失败**：任务直接失败，返回连接错误
- **B后端API验证成功**：任务继续执行
- **原因**：只认定B后端API的验证结果，确保配额数据完全一致性
- **错误信息**：
  - 未配置：`配额验证失败：配额服务未正确配置`
  - 连接失败：`配额验证失败：无法连接到配额服务`

**2. 配额扣除（updateUserUsage）- 宽松模式**
- **行为**：B后端API失败时，自动降级到KV存储模式
- **原因**：音频已生成，扣除失败不应影响用户体验
- **降级日志**：`Auto-fallback to KV storage mode`

### 测试场景

#### 场景1：B后端API未配置测试
```bash
# 1. 移除或清空B后端API配置
# B_BACKEND_API_TOKEN=  # 清空或不设置

# 2. 发起TTS任务
# 预期结果：任务立即失败，返回"配额验证失败：配额服务未正确配置"
# 用户不会收到音频，配额不会被扣除
```

#### 场景2：B后端API连接失败测试
```bash
# 1. 设置错误的B后端API配置
B_BACKEND_API_TOKEN=invalid_token

# 2. 发起TTS任务
# 预期结果：任务在配额验证阶段失败，返回"配额验证失败：无法连接到配额服务"
# 用户不会收到音频，配额不会被扣除
```

#### 场景3：配额扣除降级测试
```bash
# 1. 确保配额验证通过（使用正确的token）
# 2. 在音频生成后，模拟配额扣除API故障
# 预期结果：用户收到音频，配额扣除自动降级到KV存储
# 任务成功完成，但使用量统计可能存在延迟
```

#### 场景4：完全正常流程测试
```bash
# 1. 使用正确的B后端API配置
B_BACKEND_API_TOKEN=B_Backend_API_2024_SecureToken_X9k2#mP8$vL3nQ5@jR7wY4*tZ6

# 2. 发起TTS任务
# 预期结果：配额验证和扣除都使用B后端API，数据完全一致
```

## 验证步骤

### 1. 环境准备
确保已正确配置环境变量并部署Worker。

### 2. 基础功能验证

## 📋 改造功能清单

### ✅ 已迁移到主后端API的功能

1. **用户认证**
   - `POST /api/auth/login` - 用户登录
   - 支持用户名或邮箱登录

2. **用户注册**
   - `POST /api/auth/send-verification` - 发送注册验证码
   - `POST /api/auth/verify-email` - 验证邮箱并完成注册

3. **密码管理**
   - `POST /api/auth/change-password` - 修改密码
   - `POST /api/auth/forgot-password` - 发送重置验证码
   - `POST /api/auth/reset-password` - 重置密码

4. **配额管理**
   - `GET /api/user/quota` - 查询用户配额信息
   - 返回符合API文档的数据结构

5. **卡密激活**
   - `POST /api/card/use` - 使用卡密激活VIP

6. **配额管理（B后端API）**
   - `POST /api/b-backend/users/check-quota` - 配额预检查
   - `POST /api/b-backend/users/update-usage` - 配额扣除

### 🔄 保留在现有系统的功能

1. **TTS任务处理**
   - 所有TTS相关的核心逻辑保持不变
   - 任务状态管理继续使用KV存储
   - 音频文件存储继续使用R2

2. **WebSocket连接**
   - 实时进度更新机制保持不变

3. **重试机制**
   - 多层重试策略保持不变

## 🧪 测试验证

### 1. 功能测试

#### 测试登录接口
```bash
# 主后端API模式
curl -X POST https://your-worker.workers.dev/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "password123"}'

# 预期响应（主后端API模式）
{
  "access_token": "...",
  "refresh_token": "...",
  "username": "testuser",
  "expires_in": 7200
}
```

#### 测试配额查询接口
```bash
# 主后端API模式
curl -X GET https://your-worker.workers.dev/api/user/quota \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 预期响应（符合API文档结构）
{
  "username": "testuser",
  "vip": {
    "type": "M",
    "expireAt": 1735689600000,
    "quotaChars": 80000,
    "usedChars": 15000,
    "remainingChars": 65000,
    "usagePercentage": 18.75,
    "isLegacyUser": false,
    "isExpired": false
  },
  "usage": {
    "totalChars": 150000,
    "monthlyChars": 15000,
    "monthlyResetAt": 1753977600000
  }
}
```

#### 测试配额预检查接口（B后端API）
```bash
# 直接测试B后端API
curl -X POST https://your-main-backend.com/api/b-backend/users/check-quota \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer B_Backend_API_2024_SecureToken_X9k2#mP8$vL3nQ5@jR7wY4*tZ6" \
  -d '{
    "username": "testuser",
    "requiredTier": "STANDARD",
    "requestedChars": 1500
  }'

# 预期响应（成功）
{
  "success": true,
  "message": "配额检查通过",
  "timestamp": "2025-07-28T12:00:00.000Z"
}
```

#### 测试配额扣除接口（B后端API）
```bash
# 直接测试B后端API
curl -X POST https://your-main-backend.com/api/b-backend/users/update-usage \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer B_Backend_API_2024_SecureToken_X9k2#mP8$vL3nQ5@jR7wY4*tZ6" \
  -d '{
    "username": "testuser",
    "charCount": 1450
  }'

# 预期响应（成功）
{
  "success": true,
  "message": "使用量更新成功",
  "timestamp": "2025-07-28T12:00:00.000Z"
}
```

#### 测试TTS功能（确保不受影响）
```bash
# TTS功能应该继续正常工作
curl -X POST https://your-worker.workers.dev/api/tts \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hello, world!",
    "voice": "zh-CN-XiaoxiaoNeural",
    "model": "azure"
  }'
```

### 2. 降级测试

#### 测试KV存储降级
```bash
# 方法1：移除主后端API配置（完全降级）
# 1. 移除或清空MAIN_BACKEND_BASE环境变量
# 2. 重新部署Worker
# 3. 测试相同的接口，应该使用KV存储逻辑

# 方法2：移除B后端API配置（仅配额管理降级）
# 1. 移除或清空B_BACKEND_API_TOKEN环境变量
# 2. 重新部署Worker
# 3. 配额管理功能将降级到KV存储，其他功能继续使用主后端API

# 方法3：启用配额验证降级（推荐用于生产环境）
# 1. 设置QUOTA_FALLBACK_ENABLED=true
# 2. 重新部署Worker
# 3. 当B后端API不可用时，配额验证会自动降级到KV存储

# 方法4：新的配额管理策略（当前实现）
# 1. 配额验证始终采用严格模式，API失败时任务失败
# 2. 配额扣除始终允许降级到KV存储，确保用户体验
# 3. QUOTA_FALLBACK_ENABLED变量已废弃，不再影响行为
```

### 3. 错误处理测试

#### 测试主后端API不可用时的降级
```bash
# 设置错误的MAIN_BACKEND_BASE
MAIN_BACKEND_BASE=https://invalid-backend.com

# 测试接口应该返回适当的错误信息
```

## 🔍 监控和日志

### 关键日志点

1. **API模式检测**
   ```
   [INFO] Main backend API mode enabled: https://your-backend.com
   [INFO] Falling back to KV storage mode
   ```

2. **API调用日志**
   ```
   [INFO] Calling main backend API: /auth/login
   [ERROR] Main backend API call failed: Connection timeout
   ```

3. **降级处理日志**
   ```
   [WARN] Main backend API unavailable, using KV storage fallback
   ```

## ⚠️ 注意事项

### 1. 数据一致性
- 在迁移期间，确保主后端API和KV存储中的用户数据保持同步
- 建议在低峰期进行切换

### 2. 性能考虑
- 主后端API调用会增加网络延迟
- 建议在主后端API中实现适当的缓存策略

### 3. 错误处理
- 所有主后端API调用都有降级到KV存储的机制
- 确保主后端API的错误响应格式一致

### 4. 安全性
- JWT Token验证逻辑保持不变
- 确保主后端API使用相同的JWT_SECRET

## 🚀 部署步骤

1. **准备主后端API**
   - 确保主后端API已部署并可访问
   - 验证所有接口的响应格式符合文档要求

2. **更新环境变量**
   ```bash
   wrangler secret put MAIN_BACKEND_BASE
   # 输入: https://your-main-backend.com
   ```

3. **部署Worker**
   ```bash
   wrangler deploy
   ```

4. **验证功能**
   - 按照上述测试步骤验证所有功能
   - 确认TTS功能不受影响

5. **监控运行状态**
   - 观察日志输出
   - 监控API响应时间和错误率

## 📊 成功标准

- ✅ 所有用户认证相关接口正常工作
- ✅ 配额查询返回正确的数据结构
- ✅ TTS功能完全不受影响
- ✅ 降级机制正常工作
- ✅ 错误处理适当
- ✅ 性能在可接受范围内
