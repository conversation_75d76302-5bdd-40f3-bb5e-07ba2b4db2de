/**
 * 代理错误统计功能测试脚本
 * 测试新实现的Analytics Engine代理统计功能
 */

// 模拟环境变量
const mockEnv = {
  // 模拟Analytics Engine
  PROXY_ANALYTICS: {
    writeDataPoint: (data) => {
      console.log('📊 [ANALYTICS] 记录数据点:', JSON.stringify(data, null, 2));
      return Promise.resolve();
    }
  },
  
  // 模拟KV存储
  TTS_STATUS: {
    get: (key) => {
      console.log(`📦 [KV] 获取: ${key}`);
      return Promise.resolve('0'); // 模拟返回0
    },
    put: (key, value, options) => {
      console.log(`📦 [KV] 存储: ${key} = ${value}`, options);
      return Promise.resolve();
    }
  },
  
  DEBUG: true
};

// 模拟代理配置
const mockProxyConfig = {
  ENABLE_PROXY_STATS: true,
  ENABLE_PROXY_DEBUG: true,
  TTS_FALLBACK_WINDOW: 300 // 5分钟
};

// 模拟上下文
const mockContext = {
  taskId: 'test-task-12345',
  username: 'test-user',
  voiceId: 'test-voice-id',
  proxyUrl: 'https://proxy1.example.com'
};

// 测试函数：模拟recordProxyEvent
async function recordProxyEvent(isSuccess, env, proxyConfig, context = {}) {
  if (!proxyConfig.ENABLE_PROXY_STATS) return;

  const { error, proxyUrl, voiceId, taskId, username, responseStatus } = context;

  try {
    // 1. 保留原有的KV计数器逻辑
    const eventType = isSuccess ? 'success' : 'failure';
    const key = `proxy_stats:${eventType}:${getDateKey()}`;
    const current = await env.TTS_STATUS.get(key) || '0';
    await env.TTS_STATUS.put(key, (parseInt(current) + 1).toString(), {
      expirationTtl: 86400 // 24小时
    });

    // 2. 如果是失败，记录最近失败次数
    if (!isSuccess) {
      const recentKey = `proxy_recent_failures`;
      const recentFailures = await env.TTS_STATUS.get(recentKey) || '0';
      await env.TTS_STATUS.put(recentKey, (parseInt(recentFailures) + 1).toString(), {
        expirationTtl: proxyConfig.TTS_FALLBACK_WINDOW
      });
    }

    // 3. Analytics Engine 详细事件记录
    if (env.PROXY_ANALYTICS) {
      try {
        const blobs = [
          isSuccess ? 'success' : 'failure',                    // blob 0: event_type
          proxyUrl || 'N/A',                                    // blob 1: proxy_url
          taskId || 'N/A',                                      // blob 2: task_id
          username || 'N/A',                                    // blob 3: username
          voiceId || 'N/A',                                     // blob 4: voice_id
          isSuccess ? 'OK' : (error?.message || 'Unknown error').substring(0, 100), // blob 5: message
        ];
        
        const doubles = [
          isSuccess ? 1 : 0,                                    // double 0: success_count
          isSuccess ? 0 : 1,                                    // double 1: failure_count
          responseStatus || (error?.status || 0),               // double 2: status_code
        ];

        const indexes = [
          proxyUrl || 'N/A',                                    // index 0: proxyUrl (用于过滤)
          isSuccess ? 'SUCCESS' : 'FAILURE'                     // index 1: status (用于过滤)
        ];

        await env.PROXY_ANALYTICS.writeDataPoint({ blobs, doubles, indexes });

        if (proxyConfig.ENABLE_PROXY_DEBUG) {
          console.log(`[ANALYTICS-PROXY] 📊 Recorded ${eventType} event:`, {
            proxyUrl: proxyUrl || 'N/A',
            taskId: taskId || 'N/A',
            username: username || 'N/A',
            voiceId: voiceId || 'N/A',
            status: responseStatus || (error?.status || 0)
          });
        }
      } catch (analyticsError) {
        console.warn('[ANALYTICS-PROXY] Failed to write data point:', analyticsError.message);
      }
    }

    if (proxyConfig.ENABLE_PROXY_DEBUG) {
      const logMessage = isSuccess ? 
        `[PROXY-STATS] ✅ Recorded proxy success` : 
        `[PROXY-STATS] ❌ Recorded proxy failure`;
      
      console.log(logMessage, {
        proxyUrl: proxyUrl || 'N/A',
        error: error?.message || 'N/A',
        taskId: taskId || 'N/A'
      });
    }
  } catch (statsError) {
    console.warn('[PROXY-STATS] Failed to record event:', statsError.message);
  }
}

// 辅助函数
function getDateKey() {
  return new Date().toISOString().split('T')[0]; // YYYY-MM-DD
}

// 测试用例
async function runTests() {
  console.log('🚀 开始测试代理错误统计功能...\n');

  // 测试1: 成功事件记录
  console.log('📋 测试1: 记录代理成功事件');
  await recordProxyEvent(true, mockEnv, mockProxyConfig, {
    ...mockContext,
    responseStatus: 200
  });
  console.log('');

  // 测试2: HTTP错误事件记录
  console.log('📋 测试2: 记录HTTP错误事件');
  const httpError = new Error('Proxy failed with status 500');
  httpError.status = 500;
  await recordProxyEvent(false, mockEnv, mockProxyConfig, {
    ...mockContext,
    error: httpError,
    responseStatus: 500
  });
  console.log('');

  // 测试3: 网络错误事件记录
  console.log('📋 测试3: 记录网络错误事件');
  const networkError = new Error('Connection timeout');
  await recordProxyEvent(false, mockEnv, mockProxyConfig, {
    ...mockContext,
    error: networkError,
    responseStatus: 0
  });
  console.log('');

  // 测试4: 缺少上下文信息的情况
  console.log('📋 测试4: 缺少部分上下文信息');
  await recordProxyEvent(false, mockEnv, mockProxyConfig, {
    taskId: 'partial-context-task',
    error: new Error('Partial context test')
  });
  console.log('');

  console.log('✅ 所有测试完成！');
}

// 运行测试
runTests().catch(console.error);
