当然，这是一个非常好的优化方向。将代理选择策略从固定的“顺序故障转移”升级为可配置的“随机负载均衡+故障转移”是提升系统健壮性和性能的最佳实践。

下面我将结合您现有的代码，提供一个完整、可直接使用的最佳实践方案。

方案设计思路

我们的目标是实现一个可配置的代理选择策略，而不是简单地将代码硬编码为随机模式。这样做的好处是：

灵活性: 您可以通过环境变量轻松地在 sequential (顺序故障转移，当前行为) 和 random (随机负载均衡) 之间切换，而无需修改代码。

向后兼容: 默认行为保持不变，确保现有部署不受影响。

代码优雅: 将配置与逻辑分离，代码更清晰，易于维护。

我们将分两步实现：

增加配置: 在 getTTSProxyConfig 函数中添加一个新的配置项。

实现逻辑: 在 callTtsProxyWithFailover 函数中根据配置项选择执行路径。

实施方案：代码修改
第1步：在 getTTSProxyConfig 中添加策略配置

我们需要添加一个新的环境变量 TTS_PROXY_SELECTION_STRATEGY，并在配置函数中读取它。默认值设为 sequential 以保持当前行为。

请修改 getTTSProxyConfig 函数如下：

Generated javascript
// 【升级版】TTS 代理配置 - 支持多个备用代理 + 完全向后兼容
const getTTSProxyConfig = (env) => {
  // ... (现有代码保持不变) ...
  let proxyUrls = [];
  if (env.TTS_PROXY_URLS) {
    proxyUrls = env.TTS_PROXY_URLS
      .split(',')
      .map(url => url.trim())
      .filter(Boolean);
  } else if (env.TTS_PROXY_URL) {
    proxyUrls = [env.TTS_PROXY_URL];
  }

  return {
    // ... (所有现有配置项保持不变) ...
    TTS_PROXY_URL: proxyUrls.length > 0 ? proxyUrls[0] : null,
    TTS_PROXY_SECRET: env.TTS_PROXY_SECRET || null,
    TTS_PROXY_MODE: env.TTS_PROXY_MODE || 'fallback',
    // ...

    // ================== 【新增代码】 ==================
    /**
     * 代理选择策略
     * 'sequential': 严格按配置顺序尝试 (默认, 故障转移)
     * 'random': 每次随机打乱顺序尝试 (负载均衡 + 故障转移)
     */
    TTS_PROXY_SELECTION_STRATEGY: env.TTS_PROXY_SELECTION_STRATEGY || 'sequential',
    // ===============================================

    // ... (后续所有现有配置项保持不变) ...
    ENABLE_PROXY_STATS: env.ENABLE_PROXY_STATS !== 'false',
    ENABLE_PROXY_DEBUG: env.ENABLE_PROXY_DEBUG === 'true' || env.DEBUG === 'true'
  };
};

第2步：在 callTtsProxyWithFailover 中实现随机化逻辑

现在，我们在核心的代理调用函数中，根据上一步的配置来决定是否要打乱代理URL列表的顺序。

请修改 callTtsProxyWithFailover 函数如下：

Generated javascript
/**
 * 【升级版】调用TTS代理，支持集群级故障的退避重试
 * 在原有多代理故障转移基础上，增加集群级重试、指数退避和抖动机制
 * @param {string[]} proxyUrls - 代理服务器URL列表
 * @param {string} voiceId - 语音ID
 * @param {object} payload - 请求负载
 * @param {object} proxyConfig - 完整的代理配置对象
 * @param {object} env - 环境变量
 * @returns {Promise<ArrayBuffer>} 音频数据
 */
async function callTtsProxyWithFailover(proxyUrls, voiceId, payload, proxyConfig, env, context = {}, signal = null) {
  // 检查是否有可用的代理URL
  if (!proxyUrls || proxyUrls.length === 0) {
    throw new Error('No proxy URLs configured or available.');
  }

  // ================== 【新增/修改代码】 ==================
  let urlsToTry = proxyUrls; // 默认使用原始顺序

  // 如果策略是 'random'，则打乱数组顺序
  if (proxyConfig.TTS_PROXY_SELECTION_STRATEGY === 'random') {
    // 创建一个副本进行操作，避免修改原始传入的数组 (好习惯)
    const shuffledUrls = [...proxyUrls];
    
    // 使用 Fisher-Yates (aka Knuth) 洗牌算法，这是最高效且无偏的洗牌算法
    for (let i = shuffledUrls.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffledUrls[i], shuffledUrls[j]] = [shuffledUrls[j], shuffledUrls[i]];
    }
    
    urlsToTry = shuffledUrls;

    if (proxyConfig.ENABLE_PROXY_DEBUG) {
        console.log('[PROXY-STRATEGY] Random selection strategy enabled. Shuffled proxy order:', urlsToTry);
    }
  }
  // ======================================================

  // --- START: 新增的集群级重试逻辑 ---
  const clusterRetryAttempts = proxyConfig.TTS_CLUSTER_RETRY_COUNT;
  let lastClusterError = null;

  for (let attempt = 1; attempt <= clusterRetryAttempts; attempt++) {
    // ... (这里的代码保持不变) ...

    try {
      // ... (这里的代码保持不变) ...
      
      // 【修改点】遍历打乱后（或原始）的URL列表
      for (let i = 0; i < urlsToTry.length; i++) {
        const proxyUrl = urlsToTry[i];
        // ... (后续的 fetch 和错误处理逻辑完全保持不变) ...
      }
      // ...
    } catch (error) {
      // ... (这里的代码保持不变) ...
    }
  }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
如何使用这个新功能

保持默认行为 (顺序故障转移):

您什么都不用做。由于 TTS_PROXY_SELECTION_STRATEGY 的默认值是 'sequential'，代码的行为将和以前完全一样。

启用随机负载均衡:

在您的 Cloudflare Worker 仪表盘的 "设置" -> "变量" 中，添加一个新的环境变量。

变量名: TTS_PROXY_SELECTION_STRATEGY

变量值: random

保存并部署您的 Worker。

新方案的优势分析

这个方案是最佳实践，因为它实现了：

真正的负载均衡: 当设置为 random 时，每次TTS任务的请求流量会被均匀地分布到您配置的所有代理服务器上，避免了单一主代理过载的问题。

保留了故障转移的健壮性: 即使在随机模式下，故障转移机制依然有效。如果第一个被（随机）选中的代理失败了，系统仍然会继续尝试第二个（随机顺序中的下一个）代理，直到所有代理都尝试完毕。您同时获得了负载均衡和高可用的双重好处。

完全可控: 您可以随时通过修改一个环境变量来改变整个系统的代理行为，这在进行A/B测试、逐步上线新代理或应对突发故障时非常有用。

这个修改对您现有代码的侵入性很小，但极大地增强了代理系统的灵活性和能力。