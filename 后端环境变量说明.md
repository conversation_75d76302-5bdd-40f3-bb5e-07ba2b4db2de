# Cloudflare Worker (TTS 服务) 环境变量配置手册

本文档详细说明了部署 TTS 服务所需的全部环境变量。正确配置这些变量是保证服务稳定、高效和安全运行的关键。

## 一、核心认证与基础配置 (Core & Auth)

这些是服务运行的基础，必须配置。

| 变量名 | 作用 | 示例值 | 备注 |
|--------|------|--------|------|
| `JWT_SECRET` | **[必需]** 用于生成和验证用户登录 Token (JWT) 的密钥。必须是一个足够长且随机的字符串。 | `a_very_long_and_secret_string_12345` | 安全核心，请务必保密。 |
| `ADMIN_USERS` | **[可选]** 指定管理员用户名，多个用户用逗号分隔。拥有管理员权限的用户可以访问特殊接口（如查询所有用户用量）。 | `admin,root` | 如果不设置，管理员功能将不可用。 |
| `DEBUG` | **[可选]** 启用或禁用详细的调试日志。 | `true` | 设置为 `true` 会在 Worker 日志中输出大量过程信息，便于排查问题。生产环境建议关闭或不设置。 |
## 二、文本转语音 (TTS) 代理配置 (TTS Proxy)

这是服务中最核心、最复杂的配置部分，用于实现高可用的 TTS 生成。

### 代理功能开关配置

| 变量名 | 作用 | 开关区别 |
|--------|------|----------|
| `ENABLE_TTS_PROXY` | 代理功能总开关。 | 🔵 **`true` (推荐)**: 启用代理功能。当直连 ElevenLabs 失败时，系统会根据 `TTS_PROXY_MODE` 的策略尝试使用代理服务器。这是实现高可用的关键。<br>🔴 **`false` 或未设置**: 禁用所有代理功能。即使配置了代理URL和密钥，系统也绝不会使用代理。所有TTS请求将仅依赖直连。 |

| 变量名 | 作用 | 开关区别 |
|--------|------|----------|
| `TTS_PROXY_MODE` | 决定代理如何被使用。 | 🔵 **`proxy` 或 `proxy_only`**: 仅代理模式。所有TTS请求将跳过直连，直接通过代理服务器发送。适用于强制流量审计或直连网络完全不可用的场景。<br>🔴 **`fallback` 或未设置 (默认)**: 回退模式。系统会优先尝试直连ElevenLabs (3次)。只有当直连彻底失败且错误类型可恢复时，才会启用代理作为备用方案。这是最稳健的生产配置。<br>⚠️ **`balanced`**: (当前未实现) 此模式在代码中有设计但无逻辑实现。设置此值将等同于 `fallback` 模式。 |

### 代理服务器配置

| 变量名 | 作用 | 备注 |
|--------|------|------|
| `TTS_PROXY_URLS` | **[新, 推荐]** 配置一个或多个代理服务器的 URL，用逗号分隔。启用多代理故障转移功能。 | 示例: `"https://proxy1.example.com,https://proxy2.example.com"`<br>优先级最高。如果设置了此项，`TTS_PROXY_URL` 将被忽略。 |
| `TTS_PROXY_URL` | **[旧, 兼容]** 配置单个代理服务器的 URL。 | 示例: `"https://my-proxy.vercel.app"`<br>仅在 `TTS_PROXY_URLS` 未设置时生效，用于向后兼容。 |

### 代理参数配置

| 变量名 | 作用 | 默认值 |
|--------|------|--------|
| `TTS_PROXY_SECRET` | **[必需, 如果启用代理]** 与代理服务器约定的认证密钥。 | 无 |
| `TTS_PROXY_TIMEOUT` | 单个代理请求的超时时间（毫秒）。 | `30000` (30秒) |
| `TTS_PROXY_RETRY_COUNT` | **[仅旧配置生效]** 当使用单个代理 (`TTS_PROXY_URL`) 时，对该代理的重试次数。 | `2` |
## 三、Durable Object (DO) 位置与路由配置

用于优化性能和实现跨地域故障转移。

| 变量名 | 作用 | 开关区别 |
|--------|------|----------|
| `ENABLE_DO_LOCATION_HINT` | DO位置提示总开关。 | 🔵 **`true` 或未设置 (默认)**: 启用位置提示。系统会根据预设的优先级（亚太 > 欧洲 > 北美）为新任务选择最佳的数据中心，并支持在失败时排除故障区域进行异地重试。<br>🔴 **`false`**: 禁用位置提示。所有任务将由 Cloudflare 自动分配到“就近”的数据中心，会失去跨地域故障转移的能力。 |
| `DO_ANALYTICS` | **[可选]** 绑定一个 Cloudflare Analytics Engine 数据集，用于记录 DO 的创建意图和实际运行位置，便于分析路由效果。 | 无 |
## 四、邮件服务 (SES) 配置

用于用户注册、密码重置等功能的邮件发送。

| 变量名 | 作用 | 示例值 |
|--------|------|--------|
| `TENCENT_SECRET_ID` | **[必需, 如果使用邮件功能]** 腾讯云 API Secret ID。 | `AKID...` |
| `TENCENT_SECRET_KEY` | **[必需, 如果使用邮件功能]** 腾讯云 API Secret Key。 | `your_secret_key` |
| `SES_REGION` | **[可选]** 腾讯云 SES 服务区域。 | `ap-guangzhou` |
| `FROM_EMAIL` | **[必需, 如果使用邮件功能]** 发件人邮箱地址。 | `<EMAIL>` |
| `VERIFICATION_TEMPLATE_ID` | **[必需, 如果使用邮件功能]** 在腾讯云 SES 控制台配置的邮件模板 ID。 | `12345` |
## 五、存储与功能开关 (Storage & Features)

控制服务的其他功能和数据存储。

| 变量名 | 作用 | 备注 |
|--------|------|------|
| `VOICE_MAPPINGS` | **[必需]** 绑定一个 KV Namespace，用于存储声音名称到 Voice ID 的映射表。 | 需在 KV 中存入一个键为 `voices_v1` 的 JSON 数据。 |
| `USERS` | **[必需]** 绑定一个 KV Namespace，用于存储所有用户信息、卡密、验证码等。 | 核心数据存储，请务必备份。 |
| `TTS_STATUS` | **[必需]** 绑定一个 KV Namespace，用于存储 TTS 任务的实时状态。 | 任务处理过程中的临时数据。 |
| `CARDS` | **[必需]** 绑定一个 KV Namespace，用于存储会员卡密信息。 | |
| `AUDIOS` | **[必需]** 绑定一个 R2 Bucket，用于存储生成的音频文件。 | 核心产物存储。 |
| `AUDIO_BUCKET` | **[必需]** 绑定一个 R2 Bucket，用于存储预览音频文件。 | |
| `ENABLE_PROGRESS_MESSAGES` | **[可选]** 是否向前端发送详细的实时任务进度消息。 | `true`: 发送。`false`或未设置: 不发送。 |
| `R2_DIRECT_DOMAIN` | **[已废弃]** 旧版的 R2 直链域名。 | 新代码中已硬编码为 `r2-assets.aispeak.top`，此变量不再生效。 |
## 配置冲突与优先级说明

### 代理 URL 优先级
- **`TTS_PROXY_URLS` (新)** 的优先级高于 **`TTS_PROXY_URL` (旧)**。如果两者都设置，只有 `TTS_PROXY_URLS` 会生效。

### 代理重试配置
- **`TTS_PROXY_RETRY_COUNT`** 仅在只配置了 `TTS_PROXY_URL` (旧) 的情况下，且代码逻辑恰好回退到旧函数时才会生效。在推荐的多代理模式下，此变量无效。

---

希望这份详细的手册能帮助您更好地理解和配置服务！