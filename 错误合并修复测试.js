// 错误合并修复测试
// 验证直连失败+代理违规场景下的错误处理

console.log('🚀 开始错误合并修复测试');

// 模拟环境配置
const mockEnv = {
  DEBUG: true,
  ENABLE_TTS_PROXY: true,
  TTS_PROXY_URLS: 'https://proxy1.example.com,https://proxy2.example.com',
  TTS_PROXY_SECRET: 'test-secret',
  TTS_PROXY_TIMEOUT: 30000,
  TTS_PROXY_RETRY_COUNT: 2,
  ENABLE_PROXY_DEBUG: true,
  ELEVENLABS_API_KEY: 'test-key'
};

// 模拟代理配置获取函数
function getTTSProxyConfig(env) {
  return {
    ENABLE_TTS_PROXY: env.ENABLE_TTS_PROXY,
    TTS_PROXY_URLS: env.TTS_PROXY_URLS ? env.TTS_PROXY_URLS.split(',') : [],
    TTS_PROXY_SECRET: env.TTS_PROXY_SECRET,
    TTS_PROXY_TIMEOUT: env.TTS_PROXY_TIMEOUT,
    TTS_PROXY_RETRY_COUNT: env.TTS_PROXY_RETRY_COUNT,
    ENABLE_PROXY_DEBUG: env.ENABLE_PROXY_DEBUG,
    TTS_PROXY_MODE: env.TTS_PROXY_MODE || 'fallback'
  };
}

// 模拟shouldAttemptProxy函数
function shouldAttemptProxy(error, proxyConfig) {
  if (!proxyConfig.ENABLE_TTS_PROXY || !proxyConfig.TTS_PROXY_SECRET) {
    return false;
  }
  
  // 数据中心级别的错误（如配额限制）
  if (error.isDataCenterRetryable) {
    return true;
  }
  
  // 服务器错误（5xx）
  if (error.status >= 500) {
    return true;
  }
  
  return false;
}

// 模拟违规检测函数
function isContentViolationError(status, errorData, errorMessage) {
  if (status !== 403) return false;
  if (errorData?.detail?.status === 'content_against_policy') return true;
  const violationMessage = "We are sorry but text you are trying to use may violate our Terms of Service and has been blocked.";
  if (errorMessage && errorMessage.includes(violationMessage)) return true;
  return false;
}

// 模拟代理调用函数（返回违规错误）
async function mockCallTtsProxyWithFailover(proxyUrls, voiceId, payload, proxyConfig, env, context, signal) {
  // 模拟代理检测到违规内容
  const error = new Error('We are sorry but text you are trying to use may violate our Terms of Service and has been blocked.');
  error.status = 403;
  error.isContentViolation = true; // 关键标志
  throw error;
}

// 模拟修复后的generateSpeech函数的关键部分
async function mockGenerateSpeechErrorHandling(text, voiceId, env) {
  const proxyConfig = getTTSProxyConfig(env);
  
  // 模拟直连API失败（配额限制）
  const directError = new Error('Quota exceeded');
  directError.status = 429;
  directError.isDataCenterRetryable = true; // 这会触发代理尝试
  
  console.log('1. 模拟直连API失败（配额限制）');
  console.log(`   错误: ${directError.message}`);
  console.log(`   状态码: ${directError.status}`);
  console.log(`   可重试: ${directError.isDataCenterRetryable}`);
  
  // 检查是否应该尝试代理
  if (shouldAttemptProxy(directError, proxyConfig)) {
    console.log('2. 触发代理尝试（因为是可重试错误）');
    
    try {
      // 模拟代理调用
      await mockCallTtsProxyWithFailover(
        proxyConfig.TTS_PROXY_URLS,
        voiceId,
        { text },
        proxyConfig,
        env,
        {},
        null
      );
    } catch (proxyError) {
      console.log('3. 代理调用失败');
      console.log(`   代理错误: ${proxyError.message}`);
      console.log(`   代理状态码: ${proxyError.status}`);
      console.log(`   是否违规: ${proxyError.isContentViolation}`);
      
      // 【关键修复逻辑测试】
      console.log('\n4. 执行修复后的错误处理逻辑...');
      
      // 优先检查代理错误是否为违规
      if (proxyError.isContentViolation) {
        console.log('   ✅ 检测到代理违规错误，直接抛出保持标志完整');
        console.log(`   违规标志: ${proxyError.isContentViolation}`);
        
        // 直接抛出代理的违规错误，保持isContentViolation标志
        throw proxyError;
      }
      
      // 如果不是违规，执行原有的错误合并逻辑
      console.log('   执行错误合并逻辑（非违规情况）');
      const combinedError = new Error(`Both direct and proxy failed. Direct: ${directError.message}, Proxy: ${proxyError.message}`);
      combinedError.originalError = directError;
      combinedError.proxyError = proxyError;
      combinedError.status = directError.status;
      combinedError.isDataCenterRetryable = directError.isDataCenterRetryable;
      throw combinedError;
    }
  }
  
  // 不满足代理条件，抛出原始错误
  throw directError;
}

// 测试1: 修复前后的行为对比
async function testErrorMergingFix() {
  console.log('\n🧪 测试1: 错误合并修复验证');
  
  try {
    await mockGenerateSpeechErrorHandling('违规内容测试', 'voice1', mockEnv);
    console.log('❌ 应该抛出违规错误但没有抛出');
    return false;
  } catch (error) {
    console.log('\n5. 捕获到的最终错误:');
    console.log(`   错误消息: ${error.message}`);
    console.log(`   状态码: ${error.status}`);
    console.log(`   违规标志: ${error.isContentViolation}`);
    console.log(`   可重试标志: ${error.isDataCenterRetryable}`);
    
    // 验证修复效果
    if (error.isContentViolation === true) {
      console.log('\n✅ 修复成功！违规标志被正确保留');
      console.log('   - 代理的违规错误被直接抛出');
      console.log('   - isContentViolation标志完整保留');
      console.log('   - 上层可以正确识别违规并快速失败');
      return true;
    } else {
      console.log('\n❌ 修复失败！违规标志丢失');
      console.log('   - 错误被错误合并，丢失了违规标志');
      console.log('   - 上层无法识别违规，会继续重试');
      return false;
    }
  }
}

// 测试2: 非违规情况的兼容性测试
async function testNonViolationCompatibility() {
  console.log('\n🧪 测试2: 非违规情况的兼容性验证');
  
  // 模拟非违规的代理错误
  const mockCallTtsProxyNonViolation = async () => {
    const error = new Error('Proxy server error');
    error.status = 500;
    error.isContentViolation = false; // 非违规错误
    throw error;
  };
  
  try {
    const proxyConfig = getTTSProxyConfig(mockEnv);
    const directError = new Error('Network timeout');
    directError.status = 408;
    directError.isDataCenterRetryable = true;
    
    try {
      await mockCallTtsProxyNonViolation();
    } catch (proxyError) {
      // 测试非违规情况下的错误合并
      if (!proxyError.isContentViolation) {
        // 应该执行错误合并逻辑
        const combinedError = new Error(`Both direct and proxy failed. Direct: ${directError.message}, Proxy: ${proxyError.message}`);
        combinedError.originalError = directError;
        combinedError.proxyError = proxyError;
        combinedError.status = directError.status;
        combinedError.isDataCenterRetryable = directError.isDataCenterRetryable;
        
        console.log('✅ 非违规情况下正确执行错误合并');
        console.log(`   合并错误消息: ${combinedError.message}`);
        console.log(`   保留直连错误状态: ${combinedError.status}`);
        console.log(`   保留重试标志: ${combinedError.isDataCenterRetryable}`);
        return true;
      }
    }
  } catch (error) {
    console.log(`❌ 兼容性测试失败: ${error.message}`);
    return false;
  }
  
  return false;
}

// 测试3: 完整流程模拟
async function testCompleteFlow() {
  console.log('\n🧪 测试3: 完整流程模拟（直连失败→代理违规→快速失败）');
  
  // 模拟上层调用（如runSingleTtsProcess）
  async function mockUpperLayerCall() {
    try {
      await mockGenerateSpeechErrorHandling('包含违规内容的文本', 'voice1', mockEnv);
      return { success: true };
    } catch (error) {
      // 上层检查违规标志
      if (error.isContentViolation) {
        console.log('   🚨 上层检测到违规，立即终止重试');
        return { success: false, isViolation: true, fastFail: true };
      } else {
        console.log('   ⚠️ 上层未检测到违规，将继续重试');
        return { success: false, isViolation: false, fastFail: false };
      }
    }
  }
  
  const result = await mockUpperLayerCall();
  
  if (result.isViolation && result.fastFail) {
    console.log('✅ 完整流程测试成功');
    console.log('   - 直连API失败触发代理尝试');
    console.log('   - 代理检测到违规并返回违规错误');
    console.log('   - 违规错误被正确传播到上层');
    console.log('   - 上层识别违规并立即终止');
    return true;
  } else {
    console.log('❌ 完整流程测试失败');
    console.log(`   - 违规检测: ${result.isViolation}`);
    console.log(`   - 快速失败: ${result.fastFail}`);
    return false;
  }
}

// 运行所有测试
async function runAllTests() {
  const results = [];
  
  results.push(await testErrorMergingFix());
  results.push(await testNonViolationCompatibility());
  results.push(await testCompleteFlow());
  
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  console.log('\n' + '='.repeat(60));
  console.log(`🎯 错误合并修复测试总结: ${passed}/${total} 通过`);
  
  if (passed === total) {
    console.log('✅ 所有测试通过！错误合并修复成功。');
    console.log('\n🎉 修复效果:');
    console.log('   - 违规错误标志被正确保留');
    console.log('   - 上层可以正确识别违规并快速失败');
    console.log('   - 非违规情况的兼容性保持完整');
    console.log('   - 完整流程的快速失败机制正常工作');
  } else {
    console.log('❌ 部分测试失败，需要进一步检查。');
  }
  
  return passed === total;
}

// 执行测试
runAllTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('测试执行出错:', error);
  process.exit(1);
});
