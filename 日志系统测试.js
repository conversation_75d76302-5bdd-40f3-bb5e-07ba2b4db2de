// 日志系统功能测试脚本
// 用于验证新的统一日志记录器是否正常工作

// 模拟环境变量
const mockEnv = {
  DEBUG: 'true',
  JWT_SECRET: 'test-secret'
};

// 从worker.js复制createLogger函数进行测试
function createLogger(env) {
  const log = (level, message, data = {}, context = {}) => {
    // 只有在DEBUG模式下才输出DEBUG级别的日志
    if (level === 'DEBUG' && !(env.DEBUG === 'true' || env.DEBUG === true)) {
      return;
    }
    
    const timestamp = new Date().toISOString();
    const username = context.username || 'system';
    const taskId = context.taskId || 'N/A';
    
    // 格式化输出
    const logString = `[${level}] [${timestamp}] [user:${username}] [task:${taskId}] - ${message}`;
    
    if (Object.keys(data).length > 0) {
      console.log(logString, data);
    } else {
      console.log(logString);
    }
  };

  return {
    debug: (message, data, context) => log('DEBUG', message, data, context),
    info: (message, data, context) => log('INFO', message, data, context),
    warn: (message, data, context) => log('WARN', message, data, context),
    error: (error, context, additionalData = {}) => {
      const message = error.message || 'Unknown error';
      const data = { 
        ...additionalData,
        error: message, 
        stack: error.stack?.substring(0, 500) // 限制堆栈长度
      };
      log('ERROR', message, data, context);
    },
  };
}

function enhanceEnvWithLogging(env, logContext = {}) {
  // 创建logger实例
  const logger = createLogger(env);
  
  // 在env中注入日志相关属性
  env._logContext = logContext;
  env._logger = logger;
  
  // 提供便捷的日志方法，自动使用上下文
  env._log = {
    debug: (message, data = {}) => logger.debug(message, data, env._logContext),
    info: (message, data = {}) => logger.info(message, data, env._logContext),
    warn: (message, data = {}) => logger.warn(message, data, env._logContext),
    error: (error, additionalData = {}) => logger.error(error, env._logContext, additionalData)
  };
  
  return env;
}

// 测试函数
function testLoggingSystem() {
  console.log('=== 日志系统功能测试 ===\n');

  // 测试1: 基础日志记录器
  console.log('1. 测试基础日志记录器:');
  const logger = createLogger(mockEnv);
  const testContext = { username: 'test_user', taskId: 'task-123' };
  
  logger.info('测试信息日志', { action: 'test' }, testContext);
  logger.warn('测试警告日志', { level: 'warning' }, testContext);
  logger.debug('测试调试日志', { debug: true }, testContext);
  
  // 测试错误日志
  const testError = new Error('测试错误');
  logger.error(testError, testContext, { additionalInfo: 'extra data' });
  
  console.log('\n');

  // 测试2: 增强的环境对象
  console.log('2. 测试增强的环境对象:');
  const enhancedEnv = enhanceEnvWithLogging(mockEnv, { username: 'enhanced_user', taskId: 'task-456' });
  
  enhancedEnv._log.info('使用增强环境的信息日志', { feature: 'enhanced_env' });
  enhancedEnv._log.warn('使用增强环境的警告日志', { warning: true });
  enhancedEnv._log.debug('使用增强环境的调试日志', { debug: true });
  
  // 测试错误日志
  const enhancedError = new Error('增强环境测试错误');
  enhancedEnv._log.error(enhancedError, { source: 'enhanced_test' });
  
  console.log('\n');

  // 测试3: 无上下文的系统日志
  console.log('3. 测试无上下文的系统日志:');
  logger.info('系统级别的日志', { system: true }, {});
  
  console.log('\n');

  // 测试4: DEBUG模式关闭时的行为
  console.log('4. 测试DEBUG模式关闭时的行为:');
  const nonDebugEnv = { DEBUG: 'false' };
  const nonDebugLogger = createLogger(nonDebugEnv);
  
  console.log('应该看到INFO和WARN，但看不到DEBUG:');
  nonDebugLogger.info('这条INFO应该显示', {}, testContext);
  nonDebugLogger.warn('这条WARN应该显示', {}, testContext);
  nonDebugLogger.debug('这条DEBUG不应该显示', {}, testContext);
  
  console.log('\n=== 测试完成 ===');
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  // Node.js环境
  module.exports = { testLoggingSystem, createLogger, enhanceEnvWithLogging };
} else {
  // 浏览器环境或直接运行
  testLoggingSystem();
}
