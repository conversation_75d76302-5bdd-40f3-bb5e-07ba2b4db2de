// 配额验证条件降级功能测试脚本
// 用于验证checkVip函数的新逻辑是否正确工作

// 模拟环境变量和函数
const mockEnv1 = {
  // 场景1：配置了主后端API
  MAIN_BACKEND_BASE: 'https://api.example.com',
  B_BACKEND_API_TOKEN: 'test-token-123',
  USERS: {
    async get(key) {
      return JSON.stringify({
        vip: {
          expireAt: Date.now() + 86400000, // 24小时后过期
          type: 'S',
          quotaChars: 10000,
          usedChars: 5000
        }
      });
    }
  }
};

const mockEnv2 = {
  // 场景2：没有配置主后端API
  USERS: {
    async get(key) {
      return JSON.stringify({
        vip: {
          expireAt: Date.now() + 86400000, // 24小时后过期
          type: 'S',
          quotaChars: 10000,
          usedChars: 5000
        }
      });
    }
  }
};

const mockEnv3 = {
  // 场景3：只配置了MAIN_BACKEND_BASE，没有B_BACKEND_API_TOKEN
  MAIN_BACKEND_BASE: 'https://api.example.com',
  USERS: {
    async get(key) {
      return JSON.stringify({
        vip: {
          expireAt: Date.now() + 86400000, // 24小时后过期
          type: 'S',
          quotaChars: 10000,
          usedChars: 5000
        }
      });
    }
  }
};

// 模拟isBBackendApiEnabled函数
function isBBackendApiEnabled(env) {
  return env.MAIN_BACKEND_BASE &&
         env.MAIN_BACKEND_BASE.trim() !== '' &&
         env.B_BACKEND_API_TOKEN &&
         env.B_BACKEND_API_TOKEN.trim() !== '';
}

// 模拟callBBackendApi函数
async function callBBackendApi(endpoint, data, env) {
  console.log(`[MOCK] Calling B Backend API: ${endpoint}`, data);
  // 模拟成功响应
  return { success: true, message: 'Quota check passed' };
}

// 模拟checkVipKVMode函数
async function checkVipKVMode(username, env, requiredTier = 'STANDARD', requestedChars = 0) {
  console.log(`[MOCK] Using KV storage mode for ${username}`);
  const userDataString = await env.USERS.get(`user:${username}`);
  if (!userDataString) {
    throw new Error('用户不存在', { cause: 'quota' });
  }
  const userData = JSON.parse(userDataString);
  const vip = userData.vip;

  // 简化的检查逻辑
  if (!vip) {
    throw new Error('请先开通会员', { cause: 'quota' });
  }

  if (Date.now() > vip.expireAt) {
    throw new Error('会员已过期，请续费', { cause: 'quota' });
  }

  const currentUsed = vip.usedChars || 0;
  const totalQuota = vip.quotaChars || 0;

  if (requestedChars > 0 && currentUsed + requestedChars > totalQuota) {
    const remaining = Math.max(0, totalQuota - currentUsed);
    throw new Error(`字符数配额不足。剩余 ${remaining} 字符，本次需要 ${requestedChars} 字符。`, { cause: 'quota' });
  }

  return { success: true, mode: 'KV' };
}

// 新的checkVip函数（修改后的版本）
async function checkVip(username, env, requiredTier = 'STANDARD', requestedChars = 0) {
  // 【新逻辑】检查是否启用B后端API模式
  if (isBBackendApiEnabled(env)) {
    // 配置了主后端API，采用严格模式
    console.log(`[QUOTA-CHECK] Using B Backend API strict mode for user ${username}, tier: ${requiredTier}, chars: ${requestedChars}`);
    
    try {
      const result = await callBBackendApi('/users/check-quota', {
        username: username,
        requiredTier: requiredTier,
        requestedChars: requestedChars
      }, env);
      
      console.log(`[QUOTA-CHECK] B Backend API check passed for user ${username}`);
      return result;
    } catch (error) {
      console.error(`[QUOTA-CHECK] B Backend API failed for user ${username}:`, error.message);
      
      // 严格模式：API失败时直接让任务失败，确保不会超额使用
      console.error(`[QUOTA-CHECK] Strict mode - quota verification failed, task will be rejected for user ${username}`);
      throw new Error(`配额验证失败：无法连接到配额服务。${error.message}`, { cause: 'quota' });
    }
  } else {
    // 没有配置主后端API，降级到KV存储模式
    console.log(`[QUOTA-CHECK] Using KV storage fallback mode for user ${username}, tier: ${requiredTier}, chars: ${requestedChars}`);
    return await checkVipKVMode(username, env, requiredTier, requestedChars);
  }
}

// 测试函数
async function runTests() {
  console.log('=== 配额验证条件降级功能测试 ===\n');

  // 测试场景1：配置了完整的主后端API
  console.log('📋 测试场景1：配置了MAIN_BACKEND_BASE + B_BACKEND_API_TOKEN');
  console.log('预期：使用B后端API严格模式');
  try {
    const result1 = await checkVip('testuser1', mockEnv1, 'STANDARD', 1000);
    console.log('✅ 结果：', result1);
    console.log('✅ 测试通过：正确使用了B后端API模式\n');
  } catch (error) {
    console.log('❌ 测试失败：', error.message, '\n');
  }

  // 测试场景2：没有配置主后端API
  console.log('📋 测试场景2：没有配置MAIN_BACKEND_BASE和B_BACKEND_API_TOKEN');
  console.log('预期：降级到KV存储模式');
  try {
    const result2 = await checkVip('testuser2', mockEnv2, 'STANDARD', 1000);
    console.log('✅ 结果：', result2);
    console.log('✅ 测试通过：正确降级到KV存储模式\n');
  } catch (error) {
    console.log('❌ 测试失败：', error.message, '\n');
  }

  // 测试场景3：只配置了MAIN_BACKEND_BASE，没有B_BACKEND_API_TOKEN
  console.log('📋 测试场景3：只配置了MAIN_BACKEND_BASE，没有B_BACKEND_API_TOKEN');
  console.log('预期：降级到KV存储模式');
  try {
    const result3 = await checkVip('testuser3', mockEnv3, 'STANDARD', 1000);
    console.log('✅ 结果：', result3);
    console.log('✅ 测试通过：正确降级到KV存储模式\n');
  } catch (error) {
    console.log('❌ 测试失败：', error.message, '\n');
  }

  console.log('=== 测试完成 ===');
}

// 运行测试
runTests().catch(console.error);
