# TTS代理负载均衡功能使用说明

## 🎯 功能概述

本次更新为TTS代理系统添加了智能负载均衡功能，支持在**顺序故障转移**和**随机负载均衡**之间灵活切换，同时保持完全的向后兼容性。

## ⚙️ 配置说明

### 新增环境变量

| 变量名 | 默认值 | 可选值 | 说明 |
|--------|--------|--------|------|
| `TTS_PROXY_SELECTION_STRATEGY` | `sequential` | `sequential` \| `random` | 代理选择策略 |

### 策略详解

#### 1. `sequential` (顺序故障转移) - 默认
- **行为**: 严格按照配置的代理URL顺序进行尝试
- **适用场景**: 
  - 有主备代理的明确优先级
  - 希望优先使用性能更好的代理
  - 传统的故障转移需求
- **示例**: 总是先尝试 proxy1，失败后尝试 proxy2，最后尝试 proxy3

#### 2. `random` (随机负载均衡)
- **行为**: 每次请求随机打乱代理URL顺序，实现负载均衡
- **适用场景**:
  - 多个代理性能相近，希望均匀分配负载
  - 避免单一代理过载
  - 提高整体系统吞吐量
- **示例**: 随机选择起始代理，可能是 proxy2 → proxy1 → proxy3

## 🚀 使用方法

### 方法1: 保持默认行为（无需任何操作）
```bash
# 不设置 TTS_PROXY_SELECTION_STRATEGY 或设置为 sequential
# 系统将保持现有的顺序故障转移行为
```

### 方法2: 启用随机负载均衡
在Cloudflare Worker环境变量中添加：
```
变量名: TTS_PROXY_SELECTION_STRATEGY
变量值: random
```

## 📊 功能特性

### ✅ 核心优势
1. **真正的负载均衡**: 流量均匀分布到所有代理服务器
2. **保留故障转移**: 即使在随机模式下，故障转移机制依然有效
3. **完全向后兼容**: 默认行为不变，现有部署不受影响
4. **零侵入性**: 通过环境变量控制，无需修改代码
5. **高效算法**: 使用Fisher-Yates洗牌算法，性能优异

### 🔧 技术实现
- **算法**: Fisher-Yates (Knuth) 洗牌算法
- **内存安全**: 不修改原始数组，使用副本操作
- **调试支持**: 详细的日志输出，便于问题排查
- **集群重试**: 与现有的集群级重试机制完美集成

## 📈 性能对比

| 模式 | 负载分布 | 故障转移 | 适用场景 |
|------|----------|----------|----------|
| Sequential | 不均匀 (主要在第一个代理) | ✅ | 有明确主备优先级 |
| Random | 均匀分布 | ✅ | 代理性能相近，需要负载均衡 |

## 🔍 调试和监控

### 启用调试日志
设置环境变量：
```
ENABLE_PROXY_DEBUG=true
```

### 日志示例
```
[PROXY-STRATEGY] Random selection strategy enabled. Shuffled proxy order: [
  "https://proxy2.example.com",
  "https://proxy1.example.com", 
  "https://proxy3.example.com"
]

[PROXY-FAILOVER] 🚀 Starting multi-proxy failover with 3 proxy(ies):
{
  "proxyCount": 3,
  "proxyUrls": ["https://proxy2.example.com", "https://proxy1.example.com", "https://proxy3.example.com"],
  "originalOrder": ["https://proxy1.example.com", "https://proxy2.example.com", "https://proxy3.example.com"],
  "strategy": "random",
  "voiceId": "21m00Tcm4TlvDq8ikWAM",
  "textLength": 150
}
```

## 🛡️ 安全性和稳定性

### 安全保障
- **数组隔离**: 不修改原始配置数组
- **错误处理**: 完整保留现有错误处理机制
- **信号传递**: 支持AbortSignal和超时控制

### 稳定性保证
- **向后兼容**: 默认行为完全不变
- **渐进式升级**: 可以随时在两种模式间切换
- **故障恢复**: 保持所有现有的重试和恢复机制

## 🎯 最佳实践建议

### 何时使用Sequential模式
- 有明确的主备代理优先级
- 主代理性能明显优于备用代理
- 希望最小化代理切换开销

### 何时使用Random模式
- 多个代理性能相近
- 希望最大化系统吞吐量
- 避免单点过载问题
- 代理服务器分布在不同地理位置

### 切换建议
1. **测试环境先试**: 在测试环境验证Random模式效果
2. **监控指标**: 观察代理使用分布和响应时间
3. **渐进式部署**: 可以先在部分Worker上启用
4. **回滚准备**: 随时可以切换回Sequential模式

## 📝 配置示例

### 完整配置示例
```bash
# 基础代理配置
TTS_PROXY_URLS=https://proxy1.example.com,https://proxy2.example.com,https://proxy3.example.com
TTS_PROXY_SECRET=your-secret-key
ENABLE_TTS_PROXY=true

# 负载均衡配置
TTS_PROXY_SELECTION_STRATEGY=random

# 调试配置（可选）
ENABLE_PROXY_DEBUG=true
```

## ❓ 常见问题

**Q: 切换到Random模式会影响现有功能吗？**
A: 不会。所有现有功能（重试、超时、错误处理等）都完全保留。

**Q: Random模式下还有故障转移吗？**
A: 有。Random只是改变了尝试顺序，故障转移机制完全保留。

**Q: 如何验证负载均衡是否生效？**
A: 启用`ENABLE_PROXY_DEBUG=true`，查看日志中的代理选择顺序。

**Q: 可以随时切换模式吗？**
A: 可以。修改环境变量后重新部署即可，无需代码更改。
