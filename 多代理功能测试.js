// 多代理故障转移功能测试脚本
// 用于验证配置解析和向后兼容性

// 模拟getTTSProxyConfig函数（从worker.js复制）
const getTTSProxyConfig = (env) => {
  // 【核心升级】智能解析代理URL配置，支持新旧两种格式
  let proxyUrls = [];
  
  // 优先使用新的多URL配置 TTS_PROXY_URLS
  if (env.TTS_PROXY_URLS) {
    proxyUrls = env.TTS_PROXY_URLS
      .split(',')
      .map(url => url.trim())
      .filter(Boolean); // 移除空项
  } 
  // 向后兼容：如果没有新配置，使用旧的单URL配置
  else if (env.TTS_PROXY_URL) {
    proxyUrls = [env.TTS_PROXY_URL];
  }

  return {
    // 基础代理配置
    ENABLE_TTS_PROXY: env.ENABLE_TTS_PROXY === 'true' || env.ENABLE_TTS_PROXY === true,
    
    // 【新增】多代理URL列表（主要配置）
    TTS_PROXY_URLS: proxyUrls,
    
    // 【保留】单一代理URL（向后兼容，从列表中取第一个）
    TTS_PROXY_URL: proxyUrls.length > 0 ? proxyUrls[0] : null,
    
    TTS_PROXY_SECRET: env.TTS_PROXY_SECRET || null, // 代理认证密钥

    // 代理策略配置
    TTS_PROXY_MODE: env.TTS_PROXY_MODE || 'fallback',
    TTS_PROXY_TIMEOUT: parseInt(env.TTS_PROXY_TIMEOUT || '30000'),
    TTS_PROXY_RETRY_COUNT: parseInt(env.TTS_PROXY_RETRY_COUNT || '2'),

    // 负载均衡配置（当模式为 'balanced' 时使用）
    TTS_PROXY_BALANCE_RATIO: parseFloat(env.TTS_PROXY_BALANCE_RATIO || '0.3'),

    // 故障转移配置
    TTS_FALLBACK_THRESHOLD: parseInt(env.TTS_FALLBACK_THRESHOLD || '2'),
    TTS_FALLBACK_WINDOW: parseInt(env.TTS_FALLBACK_WINDOW || '300'),

    // 调试和监控
    ENABLE_PROXY_STATS: env.ENABLE_PROXY_STATS !== 'false',
    ENABLE_PROXY_DEBUG: env.ENABLE_PROXY_DEBUG === 'true' || env.DEBUG === 'true'
  };
};

// 测试用例
console.log('🧪 多代理故障转移功能测试\n');

// 测试1：新的多代理配置
console.log('📋 测试1：新的多代理配置');
const env1 = {
  ENABLE_TTS_PROXY: 'true',
  TTS_PROXY_URLS: 'https://proxy-a.vercel.app, https://proxy-b.onrender.com, https://proxy-c.fly.dev',
  TTS_PROXY_SECRET: 'test-secret'
};
const config1 = getTTSProxyConfig(env1);
console.log('✅ 配置结果：', {
  ENABLE_TTS_PROXY: config1.ENABLE_TTS_PROXY,
  TTS_PROXY_URLS: config1.TTS_PROXY_URLS,
  TTS_PROXY_URL: config1.TTS_PROXY_URL,
  代理数量: config1.TTS_PROXY_URLS.length
});
console.log('');

// 测试2：旧的单代理配置（向后兼容）
console.log('📋 测试2：旧的单代理配置（向后兼容）');
const env2 = {
  ENABLE_TTS_PROXY: 'true',
  TTS_PROXY_URL: 'https://legacy-proxy.vercel.app',
  TTS_PROXY_SECRET: 'test-secret'
};
const config2 = getTTSProxyConfig(env2);
console.log('✅ 配置结果：', {
  ENABLE_TTS_PROXY: config2.ENABLE_TTS_PROXY,
  TTS_PROXY_URLS: config2.TTS_PROXY_URLS,
  TTS_PROXY_URL: config2.TTS_PROXY_URL,
  代理数量: config2.TTS_PROXY_URLS.length
});
console.log('');

// 测试3：混合配置（新配置优先）
console.log('📋 测试3：混合配置（新配置优先）');
const env3 = {
  ENABLE_TTS_PROXY: 'true',
  TTS_PROXY_URLS: 'https://new-proxy-1.vercel.app,https://new-proxy-2.onrender.com',
  TTS_PROXY_URL: 'https://old-proxy.vercel.app', // 应该被忽略
  TTS_PROXY_SECRET: 'test-secret'
};
const config3 = getTTSProxyConfig(env3);
console.log('✅ 配置结果：', {
  ENABLE_TTS_PROXY: config3.ENABLE_TTS_PROXY,
  TTS_PROXY_URLS: config3.TTS_PROXY_URLS,
  TTS_PROXY_URL: config3.TTS_PROXY_URL,
  代理数量: config3.TTS_PROXY_URLS.length,
  说明: '新配置优先，旧配置被忽略'
});
console.log('');

// 测试4：空配置
console.log('📋 测试4：空配置');
const env4 = {
  ENABLE_TTS_PROXY: 'false'
};
const config4 = getTTSProxyConfig(env4);
console.log('✅ 配置结果：', {
  ENABLE_TTS_PROXY: config4.ENABLE_TTS_PROXY,
  TTS_PROXY_URLS: config4.TTS_PROXY_URLS,
  TTS_PROXY_URL: config4.TTS_PROXY_URL,
  代理数量: config4.TTS_PROXY_URLS.length
});
console.log('');

// 测试5：格式处理（空格、空项）
console.log('📋 测试5：格式处理（空格、空项）');
const env5 = {
  ENABLE_TTS_PROXY: 'true',
  TTS_PROXY_URLS: ' https://proxy-1.vercel.app , , https://proxy-2.onrender.com , ',
  TTS_PROXY_SECRET: 'test-secret'
};
const config5 = getTTSProxyConfig(env5);
console.log('✅ 配置结果：', {
  ENABLE_TTS_PROXY: config5.ENABLE_TTS_PROXY,
  TTS_PROXY_URLS: config5.TTS_PROXY_URLS,
  TTS_PROXY_URL: config5.TTS_PROXY_URL,
  代理数量: config5.TTS_PROXY_URLS.length,
  说明: '自动清理空格和空项'
});

console.log('\n🎉 所有测试完成！多代理故障转移功能配置解析正常工作。');
