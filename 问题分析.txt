【重要逻辑修正建议】
在你的新代码中，runSingleTtsProcess 和 runDialogueTtsProcess 的结构大致如下：
Generated javascript
// 在 runSingleTtsProcess 中
try {
  // ... 任务级重试的 for 循环 ...
} catch (error) {
  // ... 错误处理 ...
} finally {
  // ... 清理逻辑 (关闭WebSocket, 设置alarm) ...
}
Use code with caution.
JavaScript
这个结构是不正确的。因为 finally 会在 try 块执行完毕后（无论成功或失败）立即执行。在我们的重试逻辑中，一次失败的尝试会从 try 块中 throw error，然后 catch 住，接着 finally 就会执行，导致WebSocket在第一次失败后就被关闭了，后续的重试将无法与客户端通信。
正确的结构应该是： 将 finally 的内容移到整个函数的末尾，在重试循环之后执行。
修正方案示例 (以 runSingleTtsProcess 为例):
Generated javascript
async runSingleTtsProcess() {
    // ... 获取配置和参数 ...
    let cleanupDelay = FAILURE_CLEANUP_DELAY_MS;
    let finalResult = null;
    let finalError = null;

    try {
        // ... 任务级重试的 for 循环 ...
        for (let attempt = 1; /* ... */) {
            try {
                // ... 执行核心逻辑 ...
                const result = await this.executeSingleTtsCore(...);
                finalResult = result; // 保存成功结果
                cleanupDelay = SUCCESS_CLEANUP_DELAY_MS;
                break; // ★★★ 成功了就跳出循环 ★★★
            } catch (error) {
                finalError = error;
                // ... 记录错误，等待下一次重试 ...
                if (attempt >= maxAttempts) break;
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }

        // 循环结束后，根据结果处理
        if (finalResult) {
            // 任务最终成功，这里可以什么都不做，因为成功逻辑已在core函数中处理
        } else {
            // 所有尝试都失败了，处理最终错误
            await this.handleFinalTaskFailure(finalError, taskId, failedLocations);
        }

    } catch (outerError) {
        // 捕获顶层的、意料之外的错误
        this.logger.error(outerError, this.logContext, { message: 'Unexpected error in runSingleTtsProcess' });
        await this.handleFinalTaskFailure(outerError, taskId, []);
    } finally {
        // ★★★ 清理逻辑应该在这里 ★★★
        this.logger.info('Closing all WebSocket sessions', { /* ... */ });
        this.sessions.forEach(s => { /* ... */ });

        const cleanupTime = Date.now() + cleanupDelay;
        this.logger.info('Task finished, scheduling cleanup', { /* ... */ });
        await this.state.blockConcurrencyWhile(async () => {
            await this.state.storage.setAlarm(cleanupTime);
        });
    }
}
Use code with caution.
JavaScript
在你的新代码中，runDialogueTtsProcess函数的finally块被移除了，这可能是一个疏忽。正确的做法是，将runSingleTtsProcess函数的finally块移到整个函数的末尾，并为runDialogueTtsProcess也添加一个类似的、位于函数末尾的finally块，以确保资源总能被清理。