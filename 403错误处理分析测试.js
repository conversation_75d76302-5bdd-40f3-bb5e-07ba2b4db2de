// 403错误处理分析测试
// 验证不同类型的403错误的处理逻辑

console.log('🚀 开始403错误处理分析测试');

// 从worker.js复制关键函数
function isContentViolationError(status, errorData, errorMessage) {
  // 1. 必须是403状态码
  if (status !== 403) {
    return false;
  }

  // 2. 检查detail.status字段
  if (errorData?.detail?.status === 'content_against_policy') {
    return true;
  }

  // 3. 检查特定的违规消息
  const violationMessage = "We are sorry but text you are trying to use may violate our Terms of Service and has been blocked.";
  if (errorMessage && errorMessage.includes(violationMessage)) {
    return true;
  }

  // 4. 检查detail.message字段
  if (errorData?.detail?.message && errorData.detail.message.includes(violationMessage)) {
    return true;
  }

  // 5. 检查其他可能的违规关键词
  const violationKeywords = ["violate our Terms", "content_against_policy", "content policy violation"];
  const lowerErrorMessage = errorMessage?.toLowerCase() || '';
  if (violationKeywords.some(keyword => lowerErrorMessage.includes(keyword.toLowerCase()))) {
    return true;
  }

  return false;
}

function isDataCenterRetryableError(error, status, originalErrorData = null) {
  // 【关键】优先检查是否为内容违规错误
  if (isContentViolationError(status, originalErrorData, error.message)) {
    return false; // 内容违规错误绝对不可重试
  }
  
  // 其他重试逻辑...
  if (status === 429) return true;
  if (status === 503) return true;
  
  const errorMessage = error.message?.toLowerCase() || '';
  const retryableKeywords = [
    'quota', 'rate limit', 'too many requests', 'service unavailable'
  ];
  
  return retryableKeywords.some(keyword => errorMessage.includes(keyword));
}

function shouldAttemptProxy(error, proxyConfig) {
  // 模拟代理可用
  if (!proxyConfig.ENABLE_TTS_PROXY) return false;
  
  // 1. 数据中心级别的错误
  if (error.isDataCenterRetryable) {
    return true;
  }
  
  // 2. 服务器错误（5xx）
  if (error.status >= 500) {
    return true;
  }
  
  // 3. 特定的 4xx 错误（如 429）
  if (error.status === 429) {
    return true;
  }
  
  return false;
}

// 测试用例：不同类型的403错误
const test403Cases = [
  {
    name: '标准违规消息',
    status: 403,
    errorData: {},
    message: 'We are sorry but text you are trying to use may violate our Terms of Service and has been blocked.',
    expectedViolation: true,
    expectedRetryable: false,
    expectedProxy: false
  },
  {
    name: 'content_against_policy状态',
    status: 403,
    errorData: { detail: { status: 'content_against_policy' } },
    message: 'Content policy violation',
    expectedViolation: true,
    expectedRetryable: false,
    expectedProxy: false
  },
  {
    name: '违规关键词检测',
    status: 403,
    errorData: {},
    message: 'Your content violate our Terms of service',
    expectedViolation: true,
    expectedRetryable: false,
    expectedProxy: false
  },
  {
    name: '普通403权限错误',
    status: 403,
    errorData: { error: 'Forbidden' },
    message: 'Access denied',
    expectedViolation: false,
    expectedRetryable: false,
    expectedProxy: false
  },
  {
    name: '403 API密钥错误',
    status: 403,
    errorData: { error: 'Invalid API key' },
    message: 'Invalid API key provided',
    expectedViolation: false,
    expectedRetryable: false,
    expectedProxy: false
  },
  {
    name: '403 配额相关错误',
    status: 403,
    errorData: { detail: { status: 'quota_exceeded' } },
    message: 'Quota exceeded for this API key',
    expectedViolation: false,
    expectedRetryable: false, // 注意：403配额错误不被认为是可重试的
    expectedProxy: false
  }
];

// 测试函数
function test403ErrorHandling() {
  console.log('\n🧪 测试不同类型的403错误处理');
  
  const proxyConfig = { ENABLE_TTS_PROXY: true, TTS_PROXY_SECRET: 'test' };
  let passed = 0;
  
  test403Cases.forEach((testCase, index) => {
    console.log(`\n${index + 1}. 测试: ${testCase.name}`);
    console.log(`   状态码: ${testCase.status}`);
    console.log(`   错误消息: ${testCase.message}`);
    console.log(`   错误数据: ${JSON.stringify(testCase.errorData)}`);
    
    // 创建错误对象
    const error = new Error(testCase.message);
    error.status = testCase.status;
    
    // 测试违规检测
    const isViolation = isContentViolationError(testCase.status, testCase.errorData, testCase.message);
    console.log(`   违规检测: ${isViolation} (期望: ${testCase.expectedViolation})`);
    
    // 测试重试能力
    const isRetryable = isDataCenterRetryableError(error, testCase.status, testCase.errorData);
    error.isDataCenterRetryable = isRetryable;
    console.log(`   可重试: ${isRetryable} (期望: ${testCase.expectedRetryable})`);
    
    // 测试代理尝试
    const shouldProxy = shouldAttemptProxy(error, proxyConfig);
    console.log(`   尝试代理: ${shouldProxy} (期望: ${testCase.expectedProxy})`);
    
    // 验证结果
    const violationMatch = isViolation === testCase.expectedViolation;
    const retryableMatch = isRetryable === testCase.expectedRetryable;
    const proxyMatch = shouldProxy === testCase.expectedProxy;
    
    const allMatch = violationMatch && retryableMatch && proxyMatch;
    console.log(`   结果: ${allMatch ? '✅ 通过' : '❌ 失败'}`);
    
    if (!allMatch) {
      console.log(`   详细: 违规${violationMatch ? '✅' : '❌'} 重试${retryableMatch ? '✅' : '❌'} 代理${proxyMatch ? '✅' : '❌'}`);
    }
    
    if (allMatch) passed++;
  });
  
  console.log(`\n📊 403错误处理测试: ${passed}/${test403Cases.length} 通过`);
  return passed === test403Cases.length;
}

// 重试终止逻辑分析
function analyzeRetryTermination() {
  console.log('\n🔍 重试终止逻辑分析');
  
  console.log('\n1. 违规错误的处理路径:');
  console.log('   403 + 违规消息 → isContentViolationError() = true');
  console.log('   → isDataCenterRetryableError() = false (优先检查违规)');
  console.log('   → shouldAttemptProxy() = false (不可重试)');
  console.log('   → 设置 error.isContentViolation = true');
  console.log('   → 所有重试层级检查并立即终止');
  
  console.log('\n2. 普通403错误的处理路径:');
  console.log('   403 + 非违规消息 → isContentViolationError() = false');
  console.log('   → isDataCenterRetryableError() = false (403通常不可重试)');
  console.log('   → shouldAttemptProxy() = false (不满足代理条件)');
  console.log('   → 正常抛出错误，不进行重试');
  
  console.log('\n3. 可重试错误的处理路径:');
  console.log('   429/503/配额错误 → isDataCenterRetryableError() = true');
  console.log('   → shouldAttemptProxy() = true (满足代理条件)');
  console.log('   → 触发重试和代理尝试');
}

// 关键结论验证
function verifyKeyConclusions() {
  console.log('\n🎯 关键结论验证');
  
  // 测试1: 特定违规消息
  const violationError = new Error('We are sorry but text you are trying to use may violate our Terms of Service and has been blocked.');
  const isViolation1 = isContentViolationError(403, {}, violationError.message);
  console.log(`\n1. 特定违规消息终止重试: ${isViolation1 ? '✅ 是' : '❌ 否'}`);
  
  // 测试2: 其他违规关键词
  const violationError2 = new Error('Content violate our Terms');
  const isViolation2 = isContentViolationError(403, {}, violationError2.message);
  console.log(`2. 其他违规关键词也终止重试: ${isViolation2 ? '✅ 是' : '❌ 否'}`);
  
  // 测试3: 普通403错误
  const normalError = new Error('Access denied');
  const isViolation3 = isContentViolationError(403, {}, normalError.message);
  const isRetryable3 = isDataCenterRetryableError(normalError, 403, {});
  console.log(`3. 普通403错误不触发重试: ${!isViolation3 && !isRetryable3 ? '✅ 是' : '❌ 否'}`);
  
  // 测试4: content_against_policy状态
  const policyError = new Error('Policy violation');
  const isViolation4 = isContentViolationError(403, { detail: { status: 'content_against_policy' } }, policyError.message);
  console.log(`4. content_against_policy状态终止重试: ${isViolation4 ? '✅ 是' : '❌ 否'}`);
  
  return isViolation1 && isViolation2 && !isViolation3 && !isRetryable3 && isViolation4;
}

// 运行所有测试
function runAllTests() {
  const test1 = test403ErrorHandling();
  analyzeRetryTermination();
  const test2 = verifyKeyConclusions();
  
  console.log('\n' + '='.repeat(60));
  console.log('🎯 403错误处理分析总结:');
  console.log(`   详细测试: ${test1 ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   关键结论: ${test2 ? '✅ 验证' : '❌ 失败'}`);
  
  if (test1 && test2) {
    console.log('\n✅ 分析结论:');
    console.log('   1. 不仅仅是特定消息，多种违规检测方式都会终止重试');
    console.log('   2. 违规检测包括：特定消息、关键词、状态字段');
    console.log('   3. 普通403错误不会触发重试，直接失败');
    console.log('   4. 只有可重试错误(429/503/配额)才会触发重试和代理');
  } else {
    console.log('\n❌ 分析发现问题，需要进一步检查');
  }
  
  return test1 && test2;
}

// 执行分析
runAllTests();
