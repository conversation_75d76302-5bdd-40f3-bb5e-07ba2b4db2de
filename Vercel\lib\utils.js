import fetch from 'node-fetch';

// 【安全功能】简单的内存速率限制（适用于 Serverless）
const requestCounts = new Map();
const RATE_LIMIT_WINDOW = 60000; // 1分钟
const MAX_REQUESTS_PER_WINDOW = 3000; // 每分钟最多30个请求

export function checkRateLimit(clientId) {
  const now = Date.now();
  const windowStart = now - RATE_LIMIT_WINDOW;

  // 清理过期的记录
  for (const [id, timestamps] of requestCounts.entries()) {
    const validTimestamps = timestamps.filter(t => t > windowStart);
    if (validTimestamps.length === 0) {
      requestCounts.delete(id);
    } else {
      requestCounts.set(id, validTimestamps);
    }
  }

  // 检查当前客户端的请求频率
  const clientRequests = requestCounts.get(clientId) || [];
  const recentRequests = clientRequests.filter(t => t > windowStart);

  if (recentRequests.length >= MAX_REQUESTS_PER_WINDOW) {
    return {
      allowed: false,
      error: { error: 'Rate limit exceeded: Too many requests' }
    };
  }

  // 记录当前请求
  recentRequests.push(now);
  requestCounts.set(clientId, recentRequests);

  return { allowed: true };
}

// 【增强安全】代理密钥验证函数
export function checkProxySecret(req) {
  const incomingSecret = req.headers['x-proxy-secret'];

  // 【安全检查1】环境变量必须存在
  if (!process.env.PROXY_SECRET) {
    console.error('[SECURITY] PROXY_SECRET environment variable is not set!');
    return {
      isValid: false,
      error: { error: 'Server configuration error: Missing proxy secret' }
    };
  }

  // 【安全检查2】请求头必须存在
  if (!incomingSecret) {
    console.warn('[SECURITY] Request missing x-proxy-secret header');
    return {
      isValid: false,
      error: { error: 'Unauthorized: Missing proxy secret header' }
    };
  }

  // 【安全检查3】密钥长度检查（防止空字符串）
  if (incomingSecret.length < 8) {
    console.warn('[SECURITY] Proxy secret too short');
    return {
      isValid: false,
      error: { error: 'Unauthorized: Invalid proxy secret format' }
    };
  }

  // 调试日志（仅开发环境）
  if (process.env.NODE_ENV === 'development') {
    console.log('--- SECRET DEBUG ---');
    console.log(`Expected Secret (from env): >${process.env.PROXY_SECRET}<`);
    console.log(`Received Secret (from header): >${incomingSecret}<`);
    console.log('Are they identical?:', process.env.PROXY_SECRET === incomingSecret);
    console.log('--- END DEBUG ---');
  }

  // 【安全检查4】密钥比较（使用严格相等）
  if (incomingSecret !== process.env.PROXY_SECRET) {
    console.warn('[SECURITY] Invalid proxy secret provided');
    return {
      isValid: false,
      error: { error: 'Unauthorized: Invalid proxy secret' }
    };
  }

  // 【安全日志】记录成功的认证（生产环境）
  if (process.env.NODE_ENV === 'production') {
    console.log('[SECURITY] ✅ Proxy secret validation successful');
  }

  return { isValid: true };
}

// 处理 CORS 头
export function setCorsHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, x-proxy-secret');
}

// 处理 OPTIONS 请求
export function handleOptions(res) {
  setCorsHeaders(res);
  res.status(200).end();
}

// 错误响应处理
export function sendError(res, status, error) {
  setCorsHeaders(res);
  res.status(status).json(error);
}

// 成功响应处理
export function sendSuccess(res, data) {
  setCorsHeaders(res);
  res.status(200).json(data);
}

// 流式响应处理 - 针对 Vercel 优化
export async function handleStreamResponse(elevenLabsResponse, res) {
  try {
    // 设置响应头
    setCorsHeaders(res);
    res.writeHead(elevenLabsResponse.status, {
      'Content-Type': elevenLabsResponse.headers.get('Content-Type'),
      // 注意：不设置 Content-Length，让 Vercel 自动处理
    });

    // 【Vercel 优化】使用 Buffer 收集数据，然后一次性发送
    // 这样可以避免 Serverless 环境中的流处理问题
    const chunks = [];
    for await (const chunk of elevenLabsResponse.body) {
      chunks.push(chunk);
    }
    
    // 合并所有 chunks 并发送
    const buffer = Buffer.concat(chunks);
    res.write(buffer);
    res.end();
    
    return true;
  } catch (error) {
    console.error('[STREAM] Error processing stream:', error);
    return false;
  }
}

// ElevenLabs API 调用
export async function callElevenLabsAPI(voiceId, requestBody, timeout = 180000) {
  const elevenLabsUrl = `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}?allow_unauthenticated=1`;
  
  console.log(`[PROXY] Forwarding request to: ${elevenLabsUrl}`);

  const elevenLabsResponse = await fetch(elevenLabsUrl, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: requestBody,
    timeout: timeout,
  });

  console.log(`[PROXY] Received response from ElevenLabs with status: ${elevenLabsResponse.status}`);
  
  return elevenLabsResponse;
}
