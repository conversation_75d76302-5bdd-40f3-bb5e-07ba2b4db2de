核心问题在于：向 Cloudflare Analytics Engine 写入数据时，尝试使用了多个索引 (index)，但当前配置的 Analytics Engine 只支持最多 1 个索引。

好的，结合我们最终的分析——即在免费计划下只能使用一个索引，但依然能通过聚合和 SQL API 实现强大的分析功能——我为您整理了一套完整、具体且可直接实施的代码修改建议。

核心策略：

统一事件记录：创建一个统一的函数 recordProxyEvent 来处理所有代理的成功和失败事件。

上下文传递：修改函数签名，将包含 taskId 和 username 的 context 对象作为“接力棒”一路传递到最底层的事件记录函数。

单索引优化：在 writeDataPoint 调用中，只使用一个索引（推荐使用 proxyUrl），并将其他重要分析维度（如成功/失败状态）作为 blobs 或 doubles 记录。

代码修改建议（分步实施）
第一步：改造 recordProxy... 函数为统一的 recordProxyEvent

将现有的 recordProxySuccess 和 recordProxyFailure 统一成一个函数。这个函数将是所有分析数据的最终入口。

找到并替换 recordProxySuccess 和 recordProxyFailure 函数，使用以下代码：

Generated javascript
/**
 * 【升级版】统一的代理事件记录函数 - 支持Analytics Engine详细分析
 * @param {boolean} isSuccess - 是否成功
 * @param {object} env - 环境变量
 * @param {object} proxyConfig - 代理配置
 * @param {object} context - 上下文信息 {error, proxyUrl, voiceId, taskId, username, responseStatus}
 */
async function recordProxyEvent(isSuccess, env, proxyConfig, context = {}) {
  if (!proxyConfig.ENABLE_PROXY_STATS) return;

  const { error, proxyUrl, voiceId, taskId, username, responseStatus } = context;

  try {
    // 1. 保留原有的KV计数器逻辑（用于快速概览和向后兼容）
    const eventType = isSuccess ? 'success' : 'failure';
    const key = `proxy_stats:${eventType}:${new Date().toISOString().split('T')[0]}`;
    const current = await env.TTS_STATUS.get(key) || '0';
    await env.TTS_STATUS.put(key, (parseInt(current) + 1).toString(), {
      expirationTtl: 86400 * 2 // 保留2天
    });

    // 2. 【核心修改】Analytics Engine 详细事件记录 (单索引优化)
    if (env.PROXY_ANALYTICS) {
      try {
        const blobs = [
          isSuccess ? 'success' : 'failure',      // blob 0: event_type (用于聚合)
          proxyUrl || 'N/A',                      // blob 1: proxy_url
          taskId || 'N/A',                        // blob 2: task_id
          username || 'N/A',                      // blob 3: username
          voiceId || 'N/A',                       // blob 4: voice_id
          isSuccess ? 'OK' : (error?.message || 'Unknown error').substring(0, 100), // blob 5: message
        ];
        
        const doubles = [
          isSuccess ? 1 : 0,                      // double 0: success_count
          isSuccess ? 0 : 1,                      // double 1: failure_count
          responseStatus || (error?.status || 0), // double 2: status_code
        ];

        // 【关键】只使用一个索引，这里选择 proxyUrl，因为它最适合作为数据切入点
        const indexes = [
          proxyUrl || 'N/A',
        ];

        env.PROXY_ANALYTICS.writeDataPoint({ blobs, doubles, indexes });

        if (proxyConfig.ENABLE_PROXY_DEBUG) {
          console.log(`[ANALYTICS-PROXY] 📊 Recorded ${eventType} event for proxy: ${proxyUrl}`);
        }
      } catch (analyticsError) {
        // 如果是因为索引数量问题，这里会再次报错，但不会影响主流程
        console.warn('[ANALYTICS-PROXY] Failed to write data point:', analyticsError.message);
      }
    }
  } catch (statsError) {
    console.warn('[PROXY-STATS] Failed to record event:', statsError.message);
  }
}

第二步：修改上下文传递路径

我们需要让 context 对象从任务的最开始一直传递到代理调用层。

修改 TtsTaskDoProxy 类中的调用点
在 runSingleTtsProcess 和 runDialogueTtsProcess 方法中，找到 processChunks 的调用。

Generated javascript
// 在 TtsTaskDoProxy 类的 run...Process 方法中

// 【修改点】创建包含 taskId 和 username 的上下文
const processContext = {
  taskId: this.state.id.toString(),
  username: this.logContext.username
};

const audioDataList = await processChunks(
    chunks, voiceId, model, stability, similarity_boost, style, speed, enhancedEnv, 
    processContext // <-- 新增参数，传递上下文
);
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

修改 processChunks 函数签名
让它接收并继续传递 context。

Generated javascript
// 找到 processChunks 函数
async function processChunks(chunks, voiceId, modelId, stability, similarity_boost, style, speed, env, context = {}) { // <-- 新增 context 参数
    // ...
    const tasks = chunks.map((chunk, index) =>
        limiter(async () => {
            try {
                // ...
                // 【修改点】将 context 传递给 generateSpeech
                const audioData = await generateSpeech(chunk, voiceId, modelId, stability, similarity_boost, style, speed, chunkEnv, context); // <-- 传递 context
                // ...
            } catch (error) {
                // ...
            }
        })
    );
    // ...
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

修改 generateSpeech 函数签名
这是上下文传递的中间站。

Generated javascript
// 找到 generateSpeech 函数
async function generateSpeech(text, voiceId, modelId, stability, similarity_boost, style, speed, env, context = {}) { // <-- 新增 context 参数
    // ...
    // 在所有调用代理函数的地方（callTtsProxyWithFailover 或 callVercelProxyFallback）
    // 都要把 context 传递下去
    
    // 示例：
    fallbackResult = await callTtsProxyWithFailover(
        proxyConfig.TTS_PROXY_URLS,
        voiceId,
        payload,
        proxyConfig,
        env,
        context // <-- 传递上下文
    );
    // ...
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
第三步：改造代理调用函数以使用新事件记录器

这是最关键的一步，确保每次代理调用（无论成功或失败）都能被准确记录。

改造 callTtsProxyWithFailover (多代理模式)

Generated javascript
async function callTtsProxyWithFailover(proxyUrls, voiceId, payload, proxyConfig, env, context = {}) {
    if (!proxyUrls || proxyUrls.length === 0) {
        throw new Error('No proxy URLs configured or available.');
    }

    // ... 保留外层的集群重试和退避逻辑 ...

    // 在内层循环中
    for (let i = 0; i < proxyUrls.length; i++) {
        const proxyUrl = proxyUrls[i];
        // ...
        try {
            const response = await fetch(/* ... */);

            if (response.ok) {
                const audioBuffer = await response.arrayBuffer();
                // 【记录成功】
                const successContext = { ...context, proxyUrl, voiceId, responseStatus: response.status };
                await recordProxyEvent(true, env, proxyConfig, successContext);
                return audioBuffer;
            } else {
                const errorData = await response.text().catch(() => '');
                const errorMessage = `Proxy #${i + 1} failed with status ${response.status}`;
                const httpFailureError = new Error(errorMessage);
                httpFailureError.status = response.status;
                // 【记录HTTP失败】
                const httpFailureContext = { ...context, error: httpFailureError, proxyUrl, voiceId, responseStatus: response.status };
                await recordProxyEvent(false, env, proxyConfig, httpFailureContext);
                
                if (i === proxyUrls.length - 1) throw httpFailureError;
            }
        } catch (error) {
            // 【记录网络失败】
            const networkFailureContext = { ...context, error, proxyUrl, voiceId, responseStatus: error.status || 0 };
            await recordProxyEvent(false, env, proxyConfig, networkFailureContext);
            
            if (i === proxyUrls.length - 1) throw error;
        }
    }
    // ...
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

改造 callVercelProxyFallback (单代理兼容模式)

Generated javascript
async function callVercelProxyFallback(voiceId, payload, proxyConfig, env, context = {}) {
    // ...
    let lastError = null;
    const proxyUrl = `${proxyConfig.TTS_PROXY_URL}/api/v1/text-to-speech/${voiceId}`;
    
    while (retries > 0) {
        try {
            const response = await fetch(/* ... */);
            if (response.ok) {
                const audioBuffer = await response.arrayBuffer();
                // 【记录成功】
                const successContext = { ...context, proxyUrl: proxyConfig.TTS_PROXY_URL, voiceId, responseStatus: response.status };
                await recordProxyEvent(true, env, proxyConfig, successContext);
                return audioBuffer;
            } else {
                // ...
                throw new Error(/* ... */);
            }
        } catch (error) {
            retries--;
            lastError = error;
            if (retries === 0) {
                // 【记录最终失败】
                const failureContext = { ...context, error, proxyUrl: proxyConfig.TTS_PROXY_URL, voiceId, responseStatus: error.status || 0 };
                await recordProxyEvent(false, env, proxyConfig, failureContext);
                throw error;
            }
            // ... 退避逻辑 ...
        }
    }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
总结

完成以上三步修改后，您的代码将具备以下能力：

端到端上下文：每个TTS任务的 taskId 和 username 会从任务创建开始，一直传递到最底层的代理请求日志中。

精细化事件记录：每一次对代理的调用，无论是成功、HTTP失败还是网络失败，都会被立即记录下来，并附带丰富的上下文信息。

单索引优化：所有分析数据都写入 Analytics Engine，并使用 proxyUrl 作为唯一索引，完美兼容 Cloudflare 免费计划，同时通过 blobs 和 doubles 保留了所有分析维度。

强大的分析能力：您可以通过 Cloudflare UI 的分组聚合功能或 SQL API，轻松实现“找出失败最多的代理”、“计算各代理失败率”等高级分析。

这套方案兼顾了技术实现、成本效益和最终的分析能力，是当前情况下最优的工程实践。