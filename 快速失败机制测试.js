// 快速失败机制测试脚本
// 用于验证违规内容检测和快速失败功能

// 模拟环境配置
const mockEnv = {
  DEBUG: true,
  ENABLE_TTS_PROXY: true,
  TTS_PROXY_URLS: 'https://proxy1.example.com,https://proxy2.example.com',
  TTS_PROXY_SECRET: 'test-secret',
  TTS_PROXY_TIMEOUT: 30000,
  TTS_PROXY_RETRY_COUNT: 2,
  ENABLE_PROXY_DEBUG: true
};

// 模拟违规响应数据
const mockViolationResponses = {
  // 标准违规响应格式1
  standard403: {
    status: 403,
    data: {
      detail: {
        status: 'content_against_policy',
        message: 'We are sorry but text you are trying to use may violate our Terms of Service and has been blocked.'
      }
    }
  },
  
  // 标准违规响应格式2
  termsViolation: {
    status: 403,
    data: {
      error: 'Content violates our Terms of Service'
    }
  },
  
  // 简单违规响应
  simpleViolation: {
    status: 403,
    data: 'We are sorry but text you are trying to use may violate our Terms of Service and has been blocked.'
  },
  
  // 非违规的403错误
  non403Error: {
    status: 403,
    data: {
      error: 'Forbidden access'
    }
  },
  
  // 非403错误
  serverError: {
    status: 500,
    data: {
      error: 'Internal server error'
    }
  }
};

// 从worker.js复制isContentViolationError函数进行测试
function isContentViolationError(status, errorData, errorMessage) {
  // 1. 必须是403状态码
  if (status !== 403) {
    return false;
  }

  // 2. 检查detail.status字段
  if (errorData?.detail?.status === 'content_against_policy') {
    return true;
  }

  // 3. 检查特定的违规消息
  const violationMessage = "We are sorry but text you are trying to use may violate our Terms of Service and has been blocked.";
  if (errorMessage && errorMessage.includes(violationMessage)) {
    return true;
  }

  // 4. 检查detail.message字段
  if (errorData?.detail?.message && errorData.detail.message.includes(violationMessage)) {
    return true;
  }

  // 5. 检查其他可能的违规关键词
  const violationKeywords = ["violate our Terms", "content_against_policy", "content policy violation"];
  const lowerErrorMessage = errorMessage?.toLowerCase() || '';
  if (violationKeywords.some(keyword => lowerErrorMessage.includes(keyword.toLowerCase()))) {
    return true;
  }

  return false;
}

// 测试违规检测函数
function testViolationDetection() {
  console.log('🧪 测试违规内容检测函数...\n');
  
  const testCases = [
    {
      name: '标准违规响应格式1',
      mock: mockViolationResponses.standard403,
      expected: true
    },
    {
      name: '标准违规响应格式2',
      mock: mockViolationResponses.termsViolation,
      expected: true
    },
    {
      name: '简单违规响应',
      mock: mockViolationResponses.simpleViolation,
      expected: true
    },
    {
      name: '非违规的403错误',
      mock: mockViolationResponses.non403Error,
      expected: false
    },
    {
      name: '非403错误',
      mock: mockViolationResponses.serverError,
      expected: false
    }
  ];

  let passed = 0;
  let total = testCases.length;

  testCases.forEach((testCase, index) => {
    const { status, data } = testCase.mock;
    const errorMessage = typeof data === 'string' ? data : (data.error || data.detail?.message || '');
    
    const result = isContentViolationError(status, data, errorMessage);
    const success = result === testCase.expected;
    
    console.log(`${index + 1}. ${testCase.name}`);
    console.log(`   状态码: ${status}`);
    console.log(`   错误数据: ${JSON.stringify(data)}`);
    console.log(`   检测结果: ${result} (期望: ${testCase.expected})`);
    console.log(`   ✅ ${success ? '通过' : '❌ 失败'}\n`);
    
    if (success) passed++;
  });

  console.log(`📊 违规检测测试结果: ${passed}/${total} 通过\n`);
  return passed === total;
}

// 模拟AbortController测试
function testAbortController() {
  console.log('🧪 测试AbortController快速失败机制...\n');
  
  try {
    // 创建AbortController
    const controller = new AbortController();
    const signal = controller.signal;
    
    console.log('1. 创建AbortController: ✅');
    console.log(`   信号状态: ${signal.aborted ? '已中止' : '活跃'}`);
    
    // 测试信号组合
    const timeoutSignal = AbortSignal.timeout(5000);
    const combinedSignal = AbortSignal.any([signal, timeoutSignal]);
    
    console.log('2. 创建组合信号: ✅');
    console.log(`   组合信号状态: ${combinedSignal.aborted ? '已中止' : '活跃'}`);
    
    // 测试中止
    controller.abort();
    
    console.log('3. 执行中止操作: ✅');
    console.log(`   信号状态: ${signal.aborted ? '已中止' : '活跃'}`);
    console.log(`   组合信号状态: ${combinedSignal.aborted ? '已中止' : '活跃'}`);
    
    console.log('📊 AbortController测试: ✅ 通过\n');
    return true;
  } catch (error) {
    console.log(`❌ AbortController测试失败: ${error.message}\n`);
    return false;
  }
}

// 模拟快速失败场景测试
async function testFastFailScenario() {
  console.log('🧪 测试快速失败场景...\n');
  
  // 模拟多个并发任务
  const controller = new AbortController();
  const tasks = [];
  
  // 创建5个模拟任务
  for (let i = 0; i < 5; i++) {
    const task = new Promise((resolve, reject) => {
      const taskId = i + 1;
      
      // 监听中止信号
      controller.signal.addEventListener('abort', () => {
        console.log(`   任务${taskId}: 收到中止信号，立即停止`);
        reject(new Error(`Task ${taskId} aborted`));
      });
      
      // 模拟任务执行时间
      const delay = Math.random() * 2000 + 500; // 500-2500ms
      setTimeout(() => {
        if (!controller.signal.aborted) {
          console.log(`   任务${taskId}: 正常完成 (${Math.round(delay)}ms)`);
          resolve(`Task ${taskId} completed`);
        }
      }, delay);
      
      console.log(`   任务${taskId}: 开始执行 (预计${Math.round(delay)}ms)`);
    });
    
    tasks.push(task);
  }
  
  // 模拟在1秒后检测到违规并中止所有任务
  setTimeout(() => {
    console.log('   🚨 检测到违规内容，立即中止所有任务...');
    controller.abort();
  }, 1000);
  
  try {
    const results = await Promise.allSettled(tasks);
    
    const completed = results.filter(r => r.status === 'fulfilled').length;
    const aborted = results.filter(r => r.status === 'rejected').length;
    
    console.log(`\n📊 快速失败测试结果:`);
    console.log(`   完成任务: ${completed}`);
    console.log(`   中止任务: ${aborted}`);
    console.log(`   ✅ ${aborted > 0 ? '快速失败机制工作正常' : '❌ 快速失败机制未生效'}\n`);
    
    return aborted > 0;
  } catch (error) {
    console.log(`❌ 快速失败测试失败: ${error.message}\n`);
    return false;
  }
}

// 主测试函数
async function runAllTests() {
  console.log('🚀 开始快速失败机制测试\n');
  console.log('='.repeat(50) + '\n');
  
  const results = [];
  
  // 测试1: 违规检测函数
  results.push(testViolationDetection());
  
  // 测试2: AbortController
  results.push(testAbortController());
  
  // 测试3: 快速失败场景
  results.push(await testFastFailScenario());
  
  // 总结
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  console.log('=' * 50);
  console.log(`🎯 测试总结: ${passed}/${total} 通过`);
  
  if (passed === total) {
    console.log('✅ 所有测试通过！快速失败机制实现正确。');
  } else {
    console.log('❌ 部分测试失败，需要检查实现。');
  }
  
  return passed === total;
}

// 模拟generateSpeech函数的快速失败测试
async function testGenerateSpeechWithSignal() {
  console.log('🧪 测试generateSpeech函数的signal支持...\n');

  // 模拟generateSpeech函数
  async function mockGenerateSpeech(text, voiceId, modelId, stability, similarity_boost, style, speed, env, context = {}, signal = null) {
    return new Promise((resolve, reject) => {
      // 监听中止信号
      if (signal) {
        signal.addEventListener('abort', () => {
          console.log(`   generateSpeech: 收到中止信号，停止处理文本: "${text.substring(0, 30)}..."`);
          reject(new Error('Request aborted'));
        });
      }

      // 模拟API调用延迟
      const delay = Math.random() * 1000 + 500;
      setTimeout(() => {
        if (!signal?.aborted) {
          // 模拟违规检测
          if (text.includes('违规内容')) {
            const error = new Error('We are sorry but text you are trying to use may violate our Terms of Service and has been blocked.');
            error.status = 403;
            error.isContentViolation = true;
            reject(error);
          } else {
            resolve(new ArrayBuffer(1024)); // 模拟音频数据
          }
        }
      }, delay);
    });
  }

  const controller = new AbortController();
  const testTexts = [
    '正常文本内容',
    '违规内容测试',
    '另一个正常文本',
    '更多正常内容'
  ];

  console.log('1. 开始并发处理多个文本...');

  const tasks = testTexts.map((text, index) =>
    mockGenerateSpeech(text, 'voice1', 'model1', 0.5, 0.5, 0, 1.0, mockEnv, {}, controller.signal)
      .then(result => ({ index, text, success: true, result }))
      .catch(error => ({ index, text, success: false, error }))
  );

  try {
    const results = await Promise.allSettled(tasks);
    const processedResults = results.map(r => r.value || { success: false, error: r.reason });

    const violations = processedResults.filter(r => r.error?.isContentViolation);
    const aborted = processedResults.filter(r => r.error?.message === 'Request aborted');
    const successful = processedResults.filter(r => r.success);

    console.log(`\n📊 generateSpeech测试结果:`);
    console.log(`   成功处理: ${successful.length}`);
    console.log(`   违规检测: ${violations.length}`);
    console.log(`   被中止: ${aborted.length}`);

    processedResults.forEach(result => {
      const status = result.success ? '✅ 成功' :
                    result.error?.isContentViolation ? '🚨 违规' :
                    result.error?.message === 'Request aborted' ? '⏹️ 中止' : '❌ 失败';
      console.log(`   文本"${result.text}": ${status}`);
    });

    console.log(`   ✅ ${violations.length > 0 ? 'signal支持正常工作' : '❌ signal支持未生效'}\n`);

    return violations.length > 0;
  } catch (error) {
    console.log(`❌ generateSpeech测试失败: ${error.message}\n`);
    return false;
  }
}

// 模拟processChunks的快速失败测试
async function testProcessChunksWithFastFail() {
  console.log('🧪 测试processChunks快速失败机制...\n');

  // 模拟processChunks的核心逻辑
  async function mockProcessChunks(chunks, voiceId, modelId, stability, similarity_boost, style, speed, env, context = {}) {
    const abortController = new AbortController();
    let firstViolationError = null;

    console.log(`开始处理 ${chunks.length} 个文本块...`);

    const tasks = chunks.map((chunk, index) =>
      new Promise(async (resolve, reject) => {
        // 检查是否已被中止
        if (abortController.signal.aborted) {
          reject(new Error(`Chunk ${index + 1} cancelled due to violation`));
          return;
        }

        try {
          // 模拟处理延迟
          const delay = Math.random() * 1500 + 500;

          await new Promise(resolve => setTimeout(resolve, delay));

          // 检查是否在处理过程中被中止
          if (abortController.signal.aborted) {
            reject(new Error(`Chunk ${index + 1} cancelled during processing`));
            return;
          }

          // 模拟违规检测
          if (chunk.includes('违规')) {
            const error = new Error('Content violation detected');
            error.isContentViolation = true;
            error.status = 403;

            // 设置第一个违规错误并中止所有任务
            if (!firstViolationError) {
              firstViolationError = error;
              console.log(`   🚨 在块${index + 1}中检测到违规，中止所有任务...`);
              abortController.abort();
            }

            reject(error);
            return;
          }

          console.log(`   块${index + 1}: 处理完成`);
          resolve({ index, audioData: new ArrayBuffer(512), success: true });

        } catch (error) {
          if (error.isContentViolation && !firstViolationError) {
            firstViolationError = error;
            abortController.abort();
          }
          reject(error);
        }
      })
    );

    const results = await Promise.allSettled(tasks);

    // 优先检查违规错误
    if (firstViolationError) {
      console.log(`   🚨 传播违规错误: ${firstViolationError.message}`);
      throw firstViolationError;
    }

    return results.map(r => r.status === 'fulfilled' ? r.value : { success: false, error: r.reason });
  }

  const testChunks = [
    '这是第一个正常文本块',
    '这是第二个正常文本块',
    '这是包含违规内容的文本块',
    '这是第四个正常文本块',
    '这是第五个正常文本块'
  ];

  try {
    await mockProcessChunks(testChunks, 'voice1', 'model1', 0.5, 0.5, 0, 1.0, mockEnv, {});
    console.log('❌ 应该抛出违规错误但没有抛出\n');
    return false;
  } catch (error) {
    if (error.isContentViolation) {
      console.log(`✅ 正确捕获并传播违规错误: ${error.message}`);
      console.log('📊 processChunks快速失败测试: ✅ 通过\n');
      return true;
    } else {
      console.log(`❌ 捕获了错误但不是违规错误: ${error.message}\n`);
      return false;
    }
  }
}

// 更新主测试函数
async function runAllTests() {
  console.log('🚀 开始快速失败机制测试\n');
  console.log('=' * 50 + '\n');

  const results = [];

  // 测试1: 违规检测函数
  results.push(testViolationDetection());

  // 测试2: AbortController
  results.push(testAbortController());

  // 测试3: 快速失败场景
  results.push(await testFastFailScenario());

  // 测试4: generateSpeech signal支持
  results.push(await testGenerateSpeechWithSignal());

  // 测试5: processChunks快速失败
  results.push(await testProcessChunksWithFastFail());

  // 总结
  const passed = results.filter(Boolean).length;
  const total = results.length;

  console.log('='.repeat(50));
  console.log(`🎯 测试总结: ${passed}/${total} 通过`);

  if (passed === total) {
    console.log('✅ 所有测试通过！快速失败机制实现正确。');
    console.log('\n🎉 快速失败机制验证完成：');
    console.log('   ✅ 违规内容检测功能正常');
    console.log('   ✅ AbortController支持正常');
    console.log('   ✅ 快速失败机制工作正常');
    console.log('   ✅ generateSpeech signal支持正常');
    console.log('   ✅ processChunks快速失败正常');
  } else {
    console.log('❌ 部分测试失败，需要检查实现。');
  }

  return passed === total;
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    testViolationDetection,
    testAbortController,
    testFastFailScenario,
    testGenerateSpeechWithSignal,
    testProcessChunksWithFastFail,
    isContentViolationError
  };
} else {
  // 浏览器环境或直接运行
  console.log('开始执行测试...');
  runAllTests().then(success => {
    console.log(`测试完成，结果: ${success ? '成功' : '失败'}`);
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('测试执行出错:', error);
    process.exit(1);
  });
}
