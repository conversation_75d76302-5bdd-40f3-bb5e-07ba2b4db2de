console.log('=== 日志系统功能测试 ===');

// 模拟环境变量
const mockEnv = { DEBUG: 'true' };

// 简化的日志记录器
function createLogger(env) {
  const log = (level, message, data = {}, context = {}) => {
    if (level === 'DEBUG' && !(env.DEBUG === 'true' || env.DEBUG === true)) {
      return;
    }
    
    const timestamp = new Date().toISOString();
    const username = context.username || 'system';
    const taskId = context.taskId || 'N/A';
    
    const logString = `[${level}] [${timestamp}] [user:${username}] [task:${taskId}] - ${message}`;
    
    if (Object.keys(data).length > 0) {
      console.log(logString, data);
    } else {
      console.log(logString);
    }
  };

  return {
    info: (message, data, context) => log('INFO', message, data, context),
    warn: (message, data, context) => log('WARN', message, data, context),
    error: (error, context, additionalData = {}) => {
      const message = error.message || 'Unknown error';
      const data = { 
        ...additionalData,
        error: message
      };
      log('ERROR', message, data, context);
    }
  };
}

// 测试
const logger = createLogger(mockEnv);
const testContext = { username: 'test_user', taskId: 'task-123' };

console.log('\n1. 测试基础日志:');
logger.info('测试信息日志', { action: 'test' }, testContext);
logger.warn('测试警告日志', { level: 'warning' }, testContext);

console.log('\n2. 测试错误日志:');
const testError = new Error('测试错误');
logger.error(testError, testContext, { additionalInfo: 'extra data' });

console.log('\n3. 测试无上下文日志:');
logger.info('系统级别的日志', { system: true }, {});

console.log('\n=== 测试完成 ===');
