# 违规内容快速失败机制 - 快速参考

## 🎯 核心原理

**目标**：检测到违规内容时立即中止所有相关请求，从5-30秒等待降低到<1秒响应

**关键技术**：AbortController + 错误标志 + 优先传播

## 🔧 核心代码（5个关键函数）

### 1. 违规检测函数
```javascript
function isContentViolationError(status, errorData, errorMessage) {
  if (status !== 403) return false;
  
  // 根据你的API调整检测条件
  const violationKeywords = ["violate our Terms", "content_against_policy"];
  return violationKeywords.some(keyword => 
    errorMessage?.toLowerCase().includes(keyword.toLowerCase())
  );
}
```

### 2. API调用函数（添加signal支持）
```javascript
async function apiCall(data, options = {}, signal = null) {
  const requestConfig = {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
    signal: signal || AbortSignal.timeout(30000) // 关键：传递signal
  };
  
  const response = await fetch(options.url, requestConfig);
  
  if (!response.ok) {
    const errorData = await response.json();
    const errorMessage = errorData?.message || response.statusText;
    
    // 关键：检测违规并设置标志
    if (isContentViolationError(response.status, errorData, errorMessage)) {
      const error = new Error(errorMessage);
      error.isContentViolation = true; // 关键标志
      error.isDataCenterRetryable = false;
      throw error;
    }
  }
  
  return response;
}
```

### 3. 并发处理函数（快速失败核心）
```javascript
async function processWithFastFail(items, processor) {
  // 1. 创建中止控制器
  const abortController = new AbortController();
  let firstViolationError = null;
  
  // 2. 创建任务
  const tasks = items.map((item, index) => async () => {
    // 检查是否已被中止
    if (abortController.signal.aborted) {
      throw new Error(`Item ${index + 1} cancelled due to violation`);
    }
    
    try {
      // 传递中止信号
      return await processor(item, abortController.signal);
    } catch (error) {
      // 关键：检测违规并立即中止
      if (error.isContentViolation && !firstViolationError) {
        firstViolationError = error;
        abortController.abort(); // 立即中止所有其他任务
      }
      throw error;
    }
  });
  
  // 3. 执行任务
  const results = await Promise.allSettled(tasks.map(task => task()));
  
  // 4. 关键：优先检查违规错误
  if (firstViolationError) {
    throw firstViolationError; // 立即传播，跳过所有其他处理
  }
  
  // 5. 处理其他结果...
  return results.filter(r => r.status === 'fulfilled').map(r => r.value);
}
```

### 4. 重试逻辑更新（所有层级）
```javascript
// 数据中心级重试判断
function isDataCenterRetryable(error, status, originalErrorData) {
  if (isContentViolationError(status, originalErrorData, error.message)) {
    return false; // 违规错误绝对不重试
  }
  return error.status >= 500 || error.code === 'NETWORK_ERROR';
}

// 任务级重试循环
for (let attempt = 1; attempt <= maxAttempts; attempt++) {
  try {
    return await executeTask();
  } catch (error) {
    if (error.isContentViolation) {
      break; // 立即终止任务重试
    }
    // 其他错误继续重试...
  }
}

// 代理重试循环
for (let i = 0; i < proxies.length; i++) {
  try {
    return await callProxy(proxies[i]);
  } catch (error) {
    if (error.isContentViolation) {
      throw error; // 立即终止代理切换
    }
    // 其他错误尝试下一个代理...
  }
}
```

### 5. 错误处理更新
```javascript
function handleError(error) {
  // 关键：违规错误优先处理
  if (error.isContentViolation) {
    return {
      type: 'error',
      message: error.message,
      errorType: 'content_violation',
      retryable: false
    };
  }
  
  // 其他错误处理...
}
```

## 🚀 快速集成步骤

### 步骤1：添加检测（2分钟）
```javascript
// 复制isContentViolationError函数
// 根据你的API调整检测条件
```

### 步骤2：改造API调用（5分钟）
```javascript
// 在API调用函数中：
// 1. 添加signal参数
// 2. 在fetch中传递signal
// 3. 在错误处理中添加违规检测
// 4. 设置isContentViolation标志
```

### 步骤3：重构并发处理（10分钟）
```javascript
// 在并发处理函数中：
// 1. 创建AbortController
// 2. 在任务中传递signal
// 3. 检测违规并调用abort()
// 4. 优先处理违规错误
```

### 步骤4：更新所有重试逻辑（5分钟）
```javascript
// 1. 数据中心级重试：在isDataCenterRetryableError中检查违规
// 2. 任务级重试：在重试循环中检查error.isContentViolation并break
// 3. 代理重试：在代理循环中检查违规并立即throw
// 4. 代理集群重试：在集群重试中检查违规并终止
```

## 🛡️ 重试终止机制总览

### 四层重试终止
```javascript
// 1. 数据中心级重试终止
function isDataCenterRetryable(error) {
  if (error.isContentViolation) return false;
}

// 2. 任务级重试终止
if (error.isContentViolation) {
  break; // 终止任务重试循环
}

// 3. 代理故障转移终止
if (error.isContentViolation) {
  throw error; // 不尝试其他代理
}

// 4. 代理集群重试终止
if (error.isContentViolation) {
  throw error; // 不进行集群重试
}
```

### 终止优先级
1. **最高优先级**：违规检测 → 立即终止所有重试
2. **网络错误**：继续重试机制
3. **服务器错误**：继续重试机制
4. **其他错误**：按原有逻辑处理

## 📊 效果对比

| 指标 | 实施前 | 实施后 | 改善 |
|------|--------|--------|------|
| 违规响应时间 | 5-30秒 | <1秒 | 95%+ |
| 无效API调用 | 继续调用 | 立即停止 | 100% |
| 用户体验 | 长时间等待 | 即时反馈 | 显著提升 |
| 资源消耗 | 浪费带宽 | 立即节省 | 大幅减少 |

## ⚠️ 关键注意事项

### 1. 向后兼容
- signal参数必须可选（默认null）
- 保留所有原有错误处理逻辑
- 不影响现有API调用方式

### 2. 错误标志
- `isContentViolation: true` - 标识违规错误
- `isDataCenterRetryable: false` - 禁止重试
- 保留原始错误信息

### 3. 信号传递
- 使用`AbortSignal.any()`组合信号
- 在所有fetch调用中传递signal
- 检查`signal.aborted`状态

### 4. 优先级处理
- 违规错误优先于其他错误
- 在Promise.allSettled后立即检查
- 跳过所有后续处理逻辑

## 🧪 测试验证

### 基本测试
```javascript
// 1. 正常情况：所有任务成功完成
// 2. 违规情况：立即中止并返回违规错误
// 3. 网络错误：正常重试机制
// 4. 混合情况：违规优先于网络错误
```

### 性能测试
```javascript
// 测试违规响应时间是否<1秒
// 测试是否正确中止了其他请求
// 测试资源节省效果
```

## 🔍 调试技巧

### 关键日志点
```javascript
// 1. 违规检测时
console.log('[FAST-FAIL] Violation detected, aborting...');

// 2. 信号中止时
console.log('[FAST-FAIL] Signal aborted, cancelling task');

// 3. 错误传播时
console.log('[FAST-FAIL] Propagating violation error');
```

### 常见问题
1. **signal未传递**：检查函数签名和调用
2. **检测不准确**：调整isContentViolationError逻辑
3. **中止不生效**：检查AbortController使用
4. **错误未传播**：检查优先级处理逻辑

## 📈 监控指标

```javascript
const metrics = {
  violationCount: 0,        // 违规次数
  fastFailSavings: 0,       // 节省的请求数
  responseTimeImprovement: 0 // 响应时间改善
};
```

## 🎯 核心要点总结

1. **检测准确**：精确识别违规错误，避免误判
2. **立即中止**：使用AbortController立即停止所有相关请求
3. **优先传播**：违规错误优先于所有其他错误处理
4. **完全兼容**：不影响任何现有功能和调用方式
5. **显著提升**：用户体验提升95%+，资源大幅节省

**一句话总结**：通过AbortController + 错误标志 + 优先传播，实现违规内容的立即响应，将等待时间从分钟级降低到秒级。
