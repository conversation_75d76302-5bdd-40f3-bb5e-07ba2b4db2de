# 多代理故障转移配置指南

## 🎯 功能概述

已成功升级TTS代理系统，现在支持多个代理服务之间的自动故障转移，同时保持100%向后兼容性。

### ✨ 新功能特性

- **多代理支持**：可配置多个代理服务URL，自动故障转移
- **智能选择**：按顺序尝试代理，直到成功或全部失败
- **完全兼容**：现有单代理配置无需修改，继续正常工作
- **增强监控**：详细的故障转移日志和调试信息

## 🔧 配置方式

### 方式一：新的多代理配置（推荐）

```bash
# 基础配置
ENABLE_TTS_PROXY=true
TTS_PROXY_SECRET=your-shared-secret-key

# 多代理URL列表（逗号分隔）
TTS_PROXY_URLS=https://proxy-a.vercel.app,https://proxy-b.onrender.com,https://proxy-c.fly.dev

# 可选配置
TTS_PROXY_TIMEOUT=30000
TTS_PROXY_RETRY_COUNT=2
ENABLE_PROXY_DEBUG=true
```

### 方式二：原有单代理配置（完全兼容）

```bash
# 基础配置
ENABLE_TTS_PROXY=true
TTS_PROXY_URL=https://your-app.vercel.app
TTS_PROXY_SECRET=your-secret-key

# 可选配置
TTS_PROXY_TIMEOUT=30000
TTS_PROXY_RETRY_COUNT=2
ENABLE_PROXY_DEBUG=true
```

## 🚀 故障转移工作原理

### 1. 触发条件
代理故障转移在以下情况下自动触发：
- ElevenLabs直接调用失败（3次重试后）
- 错误类型为可重试错误（429、5xx、超时、网络错误等）
- 代理功能已启用且配置正确

### 2. 执行流程
```
ElevenLabs直接调用失败
    ↓
检查是否应该尝试代理
    ↓
多代理故障转移开始
    ↓
按顺序尝试每个代理URL
    ↓
第一个成功的代理返回结果
    ↓
如果全部失败，返回错误
```

### 3. 日志示例
```
[PROXY-TRIGGER] Direct ElevenLabs calls failed with retryable error. Triggering proxy failover...
[PROXY-FAILOVER] 🚀 Starting multi-proxy failover with 3 proxy(ies)
[PROXY-FAILOVER] 🔄 Attempting proxy #1/3: https://proxy-a.vercel.app
[PROXY-FAILOVER] ❌ Proxy #1 failed with status 503
[PROXY-FAILOVER] 🔄 Attempting proxy #2/3: https://proxy-b.onrender.com
[PROXY-FAILOVER] ✅ Proxy #2 (https://proxy-b.onrender.com) successful!
[PROXY-SUCCESS] ✅ Proxy failover successful, returning audio data
```

## 📋 配置变量详解

| 变量名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `ENABLE_TTS_PROXY` | boolean | ✅ | 启用代理功能 |
| `TTS_PROXY_URLS` | string | 🔄 | 多代理URL列表（逗号分隔） |
| `TTS_PROXY_URL` | string | 🔄 | 单代理URL（向后兼容） |
| `TTS_PROXY_SECRET` | string | ✅ | 代理认证密钥 |
| `TTS_PROXY_TIMEOUT` | number | ❌ | 代理请求超时时间（默认30000ms） |
| `TTS_PROXY_RETRY_COUNT` | number | ❌ | 代理重试次数（默认2次） |
| `ENABLE_PROXY_DEBUG` | boolean | ❌ | 启用代理调试日志 |

**注意**：`TTS_PROXY_URLS` 和 `TTS_PROXY_URL` 至少需要配置一个。

## 🎯 最佳实践

### 1. 代理服务部署建议
```bash
# 建议在不同平台部署代理服务以提高可用性
TTS_PROXY_URLS=https://proxy-main.vercel.app,https://proxy-backup.onrender.com,https://proxy-emergency.fly.dev
```

### 2. 监控和调试
```bash
# 生产环境
ENABLE_PROXY_DEBUG=false
ENABLE_PROXY_STATS=true

# 开发/测试环境
ENABLE_PROXY_DEBUG=true
DEBUG=true
```

### 3. 性能优化
```bash
# 调整超时和重试参数
TTS_PROXY_TIMEOUT=25000    # 稍微降低超时时间
TTS_PROXY_RETRY_COUNT=1    # 减少单代理重试次数（因为有多个代理）
```

## 🔄 升级指南

### 从单代理升级到多代理

1. **保持现有配置**（无需修改）
   ```bash
   # 现有配置继续工作
   ENABLE_TTS_PROXY=true
   TTS_PROXY_URL=https://your-app.vercel.app
   TTS_PROXY_SECRET=your-secret-key
   ```

2. **添加额外代理**（可选）
   ```bash
   # 添加新的多代理配置
   TTS_PROXY_URLS=https://your-app.vercel.app,https://backup.onrender.com
   
   # 原有TTS_PROXY_URL可以保留或删除
   ```

3. **验证配置**
   ```bash
   # 启用调试日志验证
   ENABLE_PROXY_DEBUG=true
   ```

## ⚠️ 注意事项

1. **密钥共享**：所有代理服务必须使用相同的 `TTS_PROXY_SECRET`
2. **API路径**：所有代理服务必须使用相同的API路径格式：`/api/v1/text-to-speech/{voiceId}`
3. **顺序重要**：代理URL按配置顺序尝试，建议将最稳定的代理放在前面
4. **资源消耗**：多代理会增加故障转移时间，建议合理配置超时参数

## 🎉 升级完成

多代理故障转移功能已成功集成，现在您的TTS服务具备了更强的容错能力和可用性保障！
