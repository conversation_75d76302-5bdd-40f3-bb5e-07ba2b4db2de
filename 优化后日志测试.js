console.log('=== 优化后日志系统功能测试 ===');

// 模拟环境变量
const mockEnv = { DEBUG: 'true' };

// 优化后的日志记录器（支持分片上下文）
function createLogger(env) {
  const log = (level, message, data = {}, context = {}) => {
    if (level === 'DEBUG' && !(env.DEBUG === 'true' || env.DEBUG === true)) {
      return;
    }
    
    const timestamp = new Date().toISOString();
    const username = context.username || 'system';
    const taskId = context.taskId || 'N/A';
    
    // 【优化】支持更精细的上下文信息
    let contextParts = `[user:${username}] [task:${taskId}]`;
    if (context.chunkIndex) {
      contextParts += ` [chunk:${context.chunkIndex}]`;
    }
    
    const logString = `[${level}] [${timestamp}] ${contextParts} - ${message}`;
    
    if (Object.keys(data).length > 0) {
      console.log(logString, data);
    } else {
      console.log(logString);
    }
  };

  return {
    debug: (message, data, context) => log('DEBUG', message, data, context),
    info: (message, data, context) => log('INFO', message, data, context),
    warn: (message, data, context) => log('WARN', message, data, context),
    error: (error, context, additionalData = {}) => {
      const message = error.message || 'Unknown error';
      const data = { 
        ...additionalData,
        error: message
      };
      log('ERROR', message, data, context);
    }
  };
}

function enhanceEnvWithLogging(env, logContext = {}) {
  const logger = createLogger(env);
  env._logContext = logContext;
  env._logger = logger;
  env._log = {
    debug: (message, data = {}) => logger.debug(message, data, env._logContext),
    info: (message, data = {}) => logger.info(message, data, env._logContext),
    warn: (message, data = {}) => logger.warn(message, data, env._logContext),
    error: (error, additionalData = {}) => logger.error(error, env._logContext, additionalData)
  };
  return env;
}

// 测试
const logger = createLogger(mockEnv);

console.log('\n1. 测试基础任务级别日志:');
const taskContext = { username: 'test_user', taskId: 'task-123' };
logger.info('任务开始处理', { action: 'start' }, taskContext);

console.log('\n2. 测试分片级别日志:');
const chunkContext = { username: 'test_user', taskId: 'task-123', chunkIndex: '1/5' };
logger.debug('处理分片', { chunkLength: 150, textPreview: 'Hello world...' }, chunkContext);
logger.warn('分片处理警告', { retryCount: 1 }, chunkContext);

console.log('\n3. 测试分片错误日志:');
const chunkError = new Error('分片处理失败');
logger.error(chunkError, chunkContext, { chunkLength: 150, textPreview: 'Hello world...' });

console.log('\n4. 测试增强环境的分片上下文:');
const enhancedEnv = enhanceEnvWithLogging(mockEnv, { username: 'enhanced_user', taskId: 'task-456' });
enhancedEnv._log.info('任务级别日志', { totalChunks: 3 });

// 模拟分片处理
for (let i = 1; i <= 3; i++) {
  const chunkLogContext = { 
    ...enhancedEnv._logContext, 
    chunkIndex: `${i}/3` 
  };
  const chunkEnv = enhanceEnvWithLogging(mockEnv, chunkLogContext);
  chunkEnv._log.debug('处理分片', { chunkLength: 100 + i * 10 });
  
  if (i === 2) {
    const error = new Error('模拟分片错误');
    chunkEnv._log.error(error, { retryAttempt: 1 });
  }
}

console.log('\n=== 测试完成 ===');
console.log('\n预期的日志格式示例:');
console.log('[INFO] [timestamp] [user:test_user] [task:task-123] - 任务开始处理');
console.log('[DEBUG] [timestamp] [user:test_user] [task:task-123] [chunk:1/5] - 处理分片');
console.log('[ERROR] [timestamp] [user:test_user] [task:task-123] [chunk:2/3] - 模拟分片错误');
