/**
 * 超时优化功能测试
 * 测试新的智能超时计算和配置功能
 */

// 模拟环境变量
const mockEnv = {
  DEBUG: 'true',
  // 使用默认值测试
  TTS_CHUNK_TIMEOUT: '50000', // 50秒每chunk
  TTS_MIN_TIMEOUT: '180000',  // 3分钟最小
  TTS_MAX_TIMEOUT: '1200000', // 20分钟最大
  TTS_ENABLE_TIMEOUT_DEBUG: 'true'
};

// 从worker.js中提取的相关函数（简化版本）
function getSmartTimeoutConfig(env) {
  return {
    INIT_TIMEOUT: parseInt(env.TTS_INIT_TIMEOUT || '30000'),
    TEXT_PROCESSING_TIMEOUT: parseInt(env.TTS_TEXT_PROCESSING_TIMEOUT || '60000'),
    AUDIO_MERGING_TIMEOUT: parseInt(env.TTS_AUDIO_MERGING_TIMEOUT || '120000'),
    R2_STORAGE_TIMEOUT: parseInt(env.TTS_R2_STORAGE_TIMEOUT || '180000'),
    DEFAULT_TIMEOUT: parseInt(env.TTS_DEFAULT_TIMEOUT || '300000'),

    CHUNK_BASE_TIMEOUT: parseInt(env.TTS_CHUNK_TIMEOUT || '40000'),
    MIN_AUDIO_TIMEOUT: parseInt(env.TTS_MIN_TIMEOUT || '120000'),
    MAX_AUDIO_TIMEOUT: parseInt(env.TTS_MAX_TIMEOUT || '900000'),

    ENABLE_COMPLEXITY_ADJUSTMENT: env.TTS_ENABLE_COMPLEXITY_ADJUSTMENT !== 'false',
    LARGE_CHUNK_THRESHOLD: parseInt(env.TTS_LARGE_CHUNK_THRESHOLD || '10'),
    HUGE_CHUNK_THRESHOLD: parseInt(env.TTS_HUGE_CHUNK_THRESHOLD || '20'),
    LARGE_TEXT_THRESHOLD: parseInt(env.TTS_LARGE_TEXT_THRESHOLD || '5000'),
    HUGE_TEXT_THRESHOLD: parseInt(env.TTS_HUGE_TEXT_THRESHOLD || '10000'),

    ENABLE_TIMEOUT_DEBUG: env.TTS_ENABLE_TIMEOUT_DEBUG === 'true' || env.DEBUG === 'true'
  };
}

function calculateAudioGenerationTimeout(taskStatus, env) {
  const timeoutConfig = getSmartTimeoutConfig(env);
  
  const chunkCount = taskStatus.totalChunks || 1;
  const totalChars = taskStatus.totalChars || 0;
  const taskType = taskStatus.taskType || 'single';
  const retryAttempt = taskStatus.retryAttempt || 1;
  
  let complexityFactor = 1.0;
  const complexityDetails = [];
  
  if (timeoutConfig.ENABLE_COMPLEXITY_ADJUSTMENT) {
    if (chunkCount > timeoutConfig.HUGE_CHUNK_THRESHOLD) {
      complexityFactor += 0.5;
      complexityDetails.push(`huge_chunks(${chunkCount}): +0.5`);
    } else if (chunkCount > timeoutConfig.LARGE_CHUNK_THRESHOLD) {
      complexityFactor += 0.3;
      complexityDetails.push(`large_chunks(${chunkCount}): +0.3`);
    }
    
    if (totalChars > timeoutConfig.HUGE_TEXT_THRESHOLD) {
      complexityFactor += 0.3;
      complexityDetails.push(`huge_text(${totalChars}): +0.3`);
    } else if (totalChars > timeoutConfig.LARGE_TEXT_THRESHOLD) {
      complexityFactor += 0.2;
      complexityDetails.push(`large_text(${totalChars}): +0.2`);
    }
    
    if (taskType === 'dialogue') {
      complexityFactor += 0.3;
      complexityDetails.push('dialogue_task: +0.3');
    }
    
    if (retryAttempt > 1) {
      const retryBonus = Math.min((retryAttempt - 1) * 0.5, 1.0);
      complexityFactor += retryBonus;
      complexityDetails.push(`retry_attempt(${retryAttempt}): +${retryBonus}`);
    }
  }
  
  complexityFactor = Math.min(complexityFactor, 2.5);
  
  const adjustedChunkTimeout = timeoutConfig.CHUNK_BASE_TIMEOUT * complexityFactor;
  const estimatedTime = Math.max(
    timeoutConfig.MIN_AUDIO_TIMEOUT,
    chunkCount * adjustedChunkTimeout
  );
  const finalTimeout = Math.min(estimatedTime, timeoutConfig.MAX_AUDIO_TIMEOUT);
  
  const calculationDetails = {
    chunkCount,
    totalChars,
    taskType,
    retryAttempt,
    baseChunkTimeout: timeoutConfig.CHUNK_BASE_TIMEOUT,
    complexityFactor: Math.round(complexityFactor * 100) / 100,
    complexityDetails,
    adjustedChunkTimeout: Math.round(adjustedChunkTimeout),
    estimatedTime: Math.round(estimatedTime),
    finalTimeout: Math.round(finalTimeout),
    minTimeout: timeoutConfig.MIN_AUDIO_TIMEOUT,
    maxTimeout: timeoutConfig.MAX_AUDIO_TIMEOUT
  };
  
  return {
    timeoutMs: finalTimeout,
    details: calculationDetails
  };
}

// 测试用例
function runTests() {
  console.log('🧪 开始超时优化功能测试...\n');

  // 测试用例1：小任务
  console.log('📝 测试用例1：小任务（3个chunk，1000字符）');
  const smallTask = {
    totalChunks: 3,
    totalChars: 1000,
    taskType: 'single',
    retryAttempt: 1
  };
  const smallResult = calculateAudioGenerationTimeout(smallTask, mockEnv);
  console.log(`⏱️  超时时间: ${Math.round(smallResult.timeoutMs/1000)}秒`);
  console.log(`📊 详情:`, smallResult.details);
  console.log('');

  // 测试用例2：中等任务
  console.log('📝 测试用例2：中等任务（15个chunk，6000字符）');
  const mediumTask = {
    totalChunks: 15,
    totalChars: 6000,
    taskType: 'single',
    retryAttempt: 1
  };
  const mediumResult = calculateAudioGenerationTimeout(mediumTask, mockEnv);
  console.log(`⏱️  超时时间: ${Math.round(mediumResult.timeoutMs/1000)}秒`);
  console.log(`📊 详情:`, mediumResult.details);
  console.log('');

  // 测试用例3：大任务
  console.log('📝 测试用例3：大任务（25个chunk，12000字符）');
  const largeTask = {
    totalChunks: 25,
    totalChars: 12000,
    taskType: 'single',
    retryAttempt: 1
  };
  const largeResult = calculateAudioGenerationTimeout(largeTask, mockEnv);
  console.log(`⏱️  超时时间: ${Math.round(largeResult.timeoutMs/1000)}秒`);
  console.log(`📊 详情:`, largeResult.details);
  console.log('');

  // 测试用例4：对话任务
  console.log('📝 测试用例4：对话任务（10个chunk，5000字符）');
  const dialogueTask = {
    totalChunks: 10,
    totalChars: 5000,
    taskType: 'dialogue',
    retryAttempt: 1
  };
  const dialogueResult = calculateAudioGenerationTimeout(dialogueTask, mockEnv);
  console.log(`⏱️  超时时间: ${Math.round(dialogueResult.timeoutMs/1000)}秒`);
  console.log(`📊 详情:`, dialogueResult.details);
  console.log('');

  // 测试用例5：重试任务
  console.log('📝 测试用例5：重试任务（10个chunk，第3次重试）');
  const retryTask = {
    totalChunks: 10,
    totalChars: 3000,
    taskType: 'single',
    retryAttempt: 3
  };
  const retryResult = calculateAudioGenerationTimeout(retryTask, mockEnv);
  console.log(`⏱️  超时时间: ${Math.round(retryResult.timeoutMs/1000)}秒`);
  console.log(`📊 详情:`, retryResult.details);
  console.log('');

  // 测试配置功能
  console.log('⚙️  测试配置功能');
  const config = getSmartTimeoutConfig(mockEnv);
  console.log('📋 当前配置:');
  console.log(`   - 每chunk基础超时: ${config.CHUNK_BASE_TIMEOUT/1000}秒`);
  console.log(`   - 最小音频超时: ${config.MIN_AUDIO_TIMEOUT/1000}秒`);
  console.log(`   - 最大音频超时: ${config.MAX_AUDIO_TIMEOUT/1000}秒`);
  console.log(`   - 复杂度调整: ${config.ENABLE_COMPLEXITY_ADJUSTMENT ? '启用' : '禁用'}`);
  console.log(`   - 调试模式: ${config.ENABLE_TIMEOUT_DEBUG ? '启用' : '禁用'}`);

  console.log('\n✅ 测试完成！');
}

// 运行测试
runTests();
